/*global loading:true, removeHash:true*/
/*
This is slowly becoming a huge monstrosity.
TODO: make this shorter or split apart, tho CTRL + F saves the day.
*/
angular.module('lmsApp')
	.controller('LearnerResourcesController', function ($scope, $http, $filter,
		$rootScope, $paymentGatewayService, smoothScroll, $textOperations, $cookies, $controller,
		$learnerOperations, $routeParams, modalService, $feedbackService, $route, $sce, $uibModal, $log,
		$timeout, $confirm, $window, $uploadService, $LearnerInterfaceFactory, $interval, $fileService, $location,
		$ngConfirm, $scheduleService, $globalPaymentService, $learningResults, $stripeService, $pay360Service, dynamicLoadingService
	) {

		$rootScope.dashboard.pkf = false;
		$scope.us = $uploadService;
		$scope.lif = $LearnerInterfaceFactory;
		$scope.fls = $fileService;
		$scope.paymentGatewayService = $paymentGatewayService;
		$scope.cancellation_reason = '';
		$scope.showDetails = false;
		$scope.custom_fields={};
		$scope.activeResource = false;
		$scope.missingResource = false;
		$scope.lops = $learnerOperations;
		$scope.ss = $scheduleService;
		$scope.loadingResource = false;
		$scope.dataLoading = true;
		$scope.couponOpt = {
			enabled: false,
			code: ""
		};
		$scope.couponExists = false;
		$scope.activeResourceId = '';
		$scope.showEventsEnrolListingModal = true;
        $scope.learningResultsService = $learningResults;
        //Custom Sort for Categories
        $scope.categoryOrderSort = function(category) {
        // If the order is 0, return Infinity so it's treated as the largest value.
       // Otherwise, return the order value itself.
         return category.order === 0 ? Infinity : category.order;
       };

		/*Check Payment response based on param*/
		$scope.paymentGatewayService.responseDisplay();
		$pay360Service.responseDisplay();

		$scope.showEportfolio = $location.path() == '/learner/resources';
		$scope.switchDisable = false;

		$scope.isCatRoute = () => $routeParams?.resource?.startsWith('cat');
		$scope.baseTitle = document.title;

		$scope.hideOtherResources = $routeParams['is_mobile'] === 'true';

		$scope.selectCategoryFilterById = (id)=>
		{
			$scope.lif.categoryFilter.selected = $rootScope.categories_all.filter(c => c.id == id)[0];
			$scope.hideResource = true;
			$scope.activeResource = null;
		}

		dynamicLoadingService.get('categories_all', '<?=$LMSUri?>learningcategory/all/no-filter').then(function(data) {
			$rootScope.categories_all = data;
			if ($scope.isCatRoute())
			{
				$scope.selectCategoryFilterById($routeParams?.resource?.split('-')[1]);
			}
		});

		dynamicLoadingService.get('standards', '<?=$LMSUri?>apprenticeshipstandards/all').then(function(data) {
			$rootScope.standards = data;
		});

		dynamicLoadingService.get('categories', '<?=$LMSUri?>learningcategory/all').then(function(data) {
			$rootScope.categories = data;
		});

		$http({
			method: 'GET',
			url: "<?=$LMSUri?>mylearning/resource-sorting-order"
		}).then(function successCallback(response) {
			let sort = response.data;
			if (!((sort == '-date_sort') || (sort == 'module.name') || (sort == '-module.name'))) sort = 'date_sort';
			$rootScope.sortResourceOrder = sort;
		});



		$scope.closeAlert = function() {
			$scope.alerts = [];
		};

		$scope.saveEventCustomFields = function(forms)
		{
			$http({
				method: 'POST',
				url: "<?=$LMSUri?>schedule/custom-field/"+$scope.activeResource.schedule_id,
				data: forms,
			}).then(function successCallback(response) {
				$scope.alerts = [{type: 'success', msg: 'Saved successfully!'}];
				setTimeout(function(){
					$scope.alerts = [];
				},5000)
			});
		}

		// This will listen for route parameter changes and will load resource/tabs as needed from route changes, no need to use on clicks everywhere.
		$scope.$on("$locationChangeStart", function (event, next, current) {
			$timeout(function () {
				var new_resource_id,
					schedule_id
					;
				// Process data only if resource is set in URL
				if (
					$routeParams.resource
					&& !$scope.isCatRoute()
					//&& $scope.activeResource
				) {
					// Get resource ID, as it can be composed from resource_id-schedule_id
					new_resource_id = parseInt($routeParams.resource.toString().split('-')[0], 10);
					// Get schedule ID, if exists
					schedule_id = $routeParams.resource.toString().split('-')[1] ? parseInt($routeParams.resource.toString().split('-')[1], 10) : false;

					// If user navigates and new resource id is not the same or schedule ID is not the same, set active resource again
					if (
						!$scope.activeResource ||
						$scope.activeResource.learning_module_id !== new_resource_id ||
						(
							schedule_id &&
							$scope.activeResourceDetails.schedule_id !== schedule_id
						)
					) {
						$scope.setActiveResource(new_resource_id, false, schedule_id);
						$scope.missingResourceCheck();
						// If active tabs are manipulated, set them!
					} else if (
						$scope.activeTab &&
						$scope.activeTab.url !== $routeParams.tab
					) {
						if ($routeParams.tab) {
							$scope.setActiveTab(false, $routeParams.tab);
						} else {
							$scope.setActiveTab($scope.resourceTabs[0]);
						}
					}
				} else {
					$scope.lif.categoryFilter.selected = null;
				}
			}, 100);
		});

		// If active resource logic is executed, but there is no active resource, show warning that either no resources assigned or incorrect URL.
		$scope.missingResourceCheck = function () {
			$scope.missingResource = false;
			// After 3 seconds when active resource call is made, show informative text on loading overlay.
			$timeout(function () {
				if (
					!$scope.activeResource &&
					!$scope.isCatRoute()
				) {
					let resourceIdPart = false; // Default value

					if ($routeParams.resource) { // Check if $routeParams.resource exists
						resourceIdPart = $routeParams.resource.toString().split('-')[1] || false;
					}

					$scope.setActiveResource($scope.route_resource_id, false, resourceIdPart);
					$timeout(function () {
						if (!$scope.activeResource) {
							$scope.missingResource = true;
						}
					}, 3000);
				}
			}, 3000);
		};

		// Learner top menu, select correct position, this is too ugly workaround
		$rootScope.$broadcast('learner-tasks-learnerresources');

		// split some functionality in seperate files, created new controller with booking classroom functionality, taken from bookmodule.js
		angular.extend(this, $controller('LearnerResourcesBookModuleController', { $scope: $scope }));

		//request all details for active module/resource
		$scope.prerequisite_counter = 0;
		$scope.setActiveResourceDetails = function () {

			// reset sign off button
			$scope.resourceSignOffTrainee = false;
			$scope.resourceSignOffManager = false;
			$scope.currentType = "resource";


			// This is lesson that is linked to event that is due for refresh, for learner.
			if ($scope.activeResource.refreshed_event) {
				$ngConfirm({
					title: $scope.activeResource.module.name,
					content: 'You need to book a training day to access the learning, use the calendar and look for an appropriate date.',
					buttons: {
						tryAgain: {
							text: 'ok',
							btnClass: 'btn-blue',
							action: function() {

							}
						},
					}
				});
				$scope.activeResource.completion_status = false;
			}
			$scope.loadingResource = true;
			$scope.activeResourceDetails = ''; // reset details
			$http.get("<?=$LMSUri?>learning/" + (
				$scope.activeResource.assigned === 0 ? 'module/' : '') +
				$scope.activeResource.module.id +
				($scope.activeResource.assigned === 0 ? '' : '/' + $rootScope.currentUser.id) +
				($scope.activeResource.assigned === 0 ? '' : '/' + $scope.activeResource.id) +
				($scope.activeResource.assigned === 0 && $scope.activeResource.schedule_id ? '/' + $scope.activeResource.schedule_id : '')
			).then(
				function (response) {
					var i,
						schedule,
						schedule_resource_links
						;
					$scope.activeResourceDetails = response.data;
					$scope.activeResourceDetails.schedule_id = $scope.activeResource.schedule_id;
					if (
						$scope.activeResourceDetails.schedule_id &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails?.learning_result?.module?.schedule_lesson_links
					) {
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links.forEach(
						(value, index) => {
							if (value.schedule.id === $scope.activeResourceDetails.schedule_id) {
								$scope.activeResourceDetails.schedule_index = index;
								$scope.activeResource.schedule_index = index;
							}
						},
					  );
					}
					//$scope.activeResourceDetails.schedule_index = $scope.activeResource.schedule_index;
					$scope.couponExists = false;

					$scope.activeResourceDetails.rejected_reasons = [];
					if(response.data.learning_results) {
						angular.forEach(response.data.learning_results, function (value) {
							if(value.reject_request_items && value.reject_request_items.rejection_reason) {
								$scope.activeResourceDetails.rejected_reasons.push({
									reject_reason: value.reject_request_items.rejection_reason
								});
							}
						});
					}

					if(response.data.learning_result && response.data.learning_result.reject_request_items) {
						$scope.activeResourceDetails.rejected_reasons.push({
							reject_reason: response.data.learning_result.reject_request_items.rejection_reason
						})
					}

					if (
						$scope.activeResourceDetails &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module &&
						$scope.activeResourceDetails.learning_result.module.is_course === 1
					) {
						$scope.lastVisitedLesson = $scope.activeResourceDetails.learning_result.module.id;
						$scope.currentType = "event";
					}

					// Check if course is signed off by learner and Coach/Trainer, I could update these variables directly without IF statement, but I might be thinking, there is reason for this!
					if (
						$scope.activeResourceDetails &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.sign_off_trainee
					) {
						$scope.resourceSignOffTrainee = true;
					}

					if (
						$scope.activeResourceDetails &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.sign_off_manager
					) {
						$scope.resourceSignOffManager = true;
					}

					if (
						$scope.activeResourceDetails &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module &&
						$scope.activeResourceDetails.learning_result.module.type &&
						$scope.activeResourceDetails.learning_result.module.type.id
					) {
						$scope.activeResourceDetails.learning_result.module.type.id = parseInt($scope.activeResourceDetails.learning_result.module.type.id, 10);
					}


					$scope.uncompletedPrerequisites = false;
					$scope.uncompletedRequiredModules = false;

					// set up dummy properties that I use in various places, in case resource is not assigned, these properties are missing
					if ($scope.activeResource.assigned === 0) {
						$scope.activeResourceDetails.learning_result = {
							module: {
								type: {

								},
								modules: {

								},
								prerequisites: {

								}
							}
						};

						// If this lesson/event can be enrollable, clone schedule_lesson_links inside module
						if (
							$scope.activeResourceDetails.schedule_lesson_links &&
							$scope.activeResourceDetails.schedule_lesson_links.length > 0
						) {
							$scope.activeResourceDetails.learning_result.module.schedule_lesson_links = $scope.activeResourceDetails.schedule_lesson_links;
						}
					}

					delete $scope.activeResourceDetailsPrerequisites;

					if (
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module
					) {
						$scope.activeResourceDetailsPrerequisites = $scope.activeResourceDetails.learning_result.module.prerequisites;
						$scope.prerequisite_counter = $scope.prerequisite_counter + 1;
					}



					// Check if required_modules is inside module object, in case this resource is part of lesson where resources have to be completed in order.
					if (
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module &&
						$scope.activeResourceDetails.learning_result.module.required_modules &&
						$scope.activeResourceDetails.learning_result.module.required_modules.length > 0
					) {
						angular.forEach($scope.activeResourceDetails.learning_result.module.required_modules, function (prerequisite) {
							if (
								!prerequisite.learning_result ||
								prerequisite.learning_result.completion_status !== "completed"
							) {
								$scope.uncompletedRequiredModules = true;
							}
						});
					}

					// List all prerequisites for learning resource
					if (
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module &&
						$scope.activeResourceDetails.learning_result.module.do_prerequisite
					) {
						angular.forEach($scope.activeResourceDetails.learning_result.module.prerequisites, function (prerequisite) {
							if (
								!prerequisite.learning_result ||
								prerequisite.learning_result.completion_status !== "completed"
							) {
								$scope.uncompletedPrerequisites = true;
							}
						});
					}

					// If this is course and there is event attached with unique resources, replace "module.modules" with "module->ScheduleLessonLink->Schedule->Resources"
					// Lots of iffs
					if (
						(
							$scope.activeResource.schedule_index ||
							$scope.activeResource.schedule_index === 0
						) &&
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails.learning_result.module &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links.length > 0 &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index] &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule
					) {
						schedule = $scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule;

						$scope.activeResourceDetails.venue_details = false;
						if(schedule.venue_deatils) {
							$scope.activeResourceDetails.venue_details = schedule.venue_deatils;
							if(schedule.venue_deatils.image) {
								$scope.activeResourceDetails.venue_details.image_url = '<?=$LMSUri?>venue/'+schedule?.venue_deatils?.image || null;
							}
						}

						// Reset modules to empty array
						$scope.activeResourceDetails.learning_result.module.modules = [];

						$scope.activeResourceDetails.instructor_lead = false;
						// Loop event link resources and add them into $scope.activeResourceDetails.learning_result.module.modules

						if (schedule.resource_links) {
							schedule_resource_links = schedule.resource_links;
							// Sort event link to resource by order
							schedule_resource_links.sort(function (a, b) {
								return a.order < b.order;
							});

							for (i = schedule_resource_links.length - 1; i >= 0; i--) {
								if (schedule_resource_links[i].resource) {
									schedule_resource_links[i].resource.instructor_lead = schedule_resource_links[i].instructor_lead;
									schedule_resource_links[i].resource.learning_result.date_due = schedule.start_date;
									// add event/schedule resources to active lesson modules
									$scope.activeResourceDetails.learning_result.module.modules.push(schedule_resource_links[i].resource);

									// also check if at least one of resources are video conferencing type
									if (
										schedule_resource_links[i].resource &&
										schedule_resource_links[i].resource.type &&
										(
											schedule_resource_links[i].resource.type.slug === 'zoom_meeting' ||
											schedule_resource_links[i].resource.type.slug === 'microsoft_teams'
										)
									) {
										schedule.video_conferencing = true;
									}
									// And check if any resource is marked as instructor_lead
									if (schedule_resource_links[i].instructor_lead) {
										schedule.instructor_lead = true;
									}

								}
							}

						}


						// If one of resources in lesson is video conferencing and one is instructor lead, then this lesson will be lead by instructor and will look different.
						if (
							schedule.video_conferencing &&
							schedule.instructor_lead
						) {
							$scope.activeResourceDetails.instructor_lead = true;
						}

						if (schedule.user_link) {
							// Replace activeResourceDetails completion status from user-schedule link
							$scope.activeResourceDetails.learning_result.completion_status = schedule.user_link.completion_status.toLowerCase();
						}
					}

					// List all modules for lesson
					if (
						$scope.activeResourceDetails.learning_result &&
						$scope.activeResourceDetails?.learning_result?.module?.modules?.length > 0
					) {
						angular.forEach($scope.activeResourceDetails.learning_result.module.modules, function (prerequisite) {
							if (
								!prerequisite.learning_result ||
								prerequisite.learning_result.completion_status !== "completed"
							) {
								$scope.uncompletedPrerequisites = true;
							}
						});
					}

					//load booking data for classroom
					if ($scope.checkType('classroom')) {
						$scope.loadBookingData();
					}


					// check %%make_payment%% status - if payment need to performed - check coupon assigned
					let activeResourceDetails = $scope.activeResourceDetails;
					let activeResource = $scope.activeResource;
					let scheduleLessonLinks = activeResourceDetails?.learning_result?.module?.schedule_lesson_links ?? false;
					let scheduleIndex = activeResource.schedule_index ?? false;
					if (scheduleLessonLinks && scheduleIndex) {
						let userLink = scheduleLessonLinks[scheduleIndex]?.schedule?.user_link ?? false;
						if(userLink) {
							let isApprovedAndUnpaidUserLink = userLink?.approved != 0 && userLink?.is_paid == '0';
							let isApprovedAndUnpaidLearningResult = activeResourceDetails.learning_result.approved != 0 && activeResourceDetails.learning_result.is_paid == 0;

							if (
								$rootScope.config.isCivicaPaymentsEngine ||
								$rootScope.config.isGlobalPaymentsEngine ||
								$rootScope.config.enablePay360
							) {
								if (isApprovedAndUnpaidUserLink || isApprovedAndUnpaidLearningResult) {
									let scheduleId = scheduleLessonLinks[scheduleIndex]?.schedule_id;
									if (scheduleId) {
										$scope.activeResourceId = scheduleId;
										$scope.getCouponAssignedForResource(scheduleId);
									}
								}
							}
						}
					}

					$scope.launchResourceSet();
					$scope.tabConditions();
					$scope.checkPrint();
					$scope.loadComments();

					// Check id:learning-resource-mini-description height, if exceeds certain threashold, show read more button on description, sidebar, main window
					$scope.showReadMoreDesciption = false;
					$timeout(function () {
						var height_element = document.getElementById('learning-resource-mini-description');
						if (
							height_element &&
							height_element.offsetHeight > 110
						) {
							$scope.showReadMoreDesciption = true;
						}
					}, 100);

					// If launch hash is present in url, launch resource and remove hash!
					$timeout(function () {
						if (
							$rootScope.config.startupCourseID &&
							$rootScope.config.startupCourseID === $scope.activeResourceDetails.learning_result.learning_module_id &&
							$rootScope.startupCourseIDLaunched !== true &&
							$scope.activeResourceDetails.learning_result.completion_status !== 'completed'
						) {
							$rootScope.startupCourseIDLaunched = true;
							$scope.launchResource();
						}
					}, 100);
					$rootScope.$broadcast('active-resource-details-set', $scope.activeResourceDetails);

					$timeout(function () {
						var button = document.getElementById('launch-resource-button');
						if (button) button.focus();
					}, 500);

					$scope.loadingResource = false;
					if ($rootScope.config.showDetailsTabForUploadResource && $scope.checkType('upload') && $scope.activeTab !== 'details' && $scope.resources.progress() == 'Information Only'){
						$scope.setActiveTab($scope.resourceTabs[1], false, true);
					}
				}
			);
		};

		$scope.getCouponAssignedForResource = function (resourceId) {
			$globalPaymentService.getCouponAssignedForResource(resourceId, 'schedule')
				.then(function successCallback(response) {
					$scope.couponExists = response.data.coupon_exists
					$scope.dataLoading = false;
				});
		};

		$scope.applyCoupon = function () {
			if($scope.activeResourceId == '') {
				return ;
			}
			$scope.dataLoading = true;
			$globalPaymentService.applyCoupon({resource_id: $scope.activeResourceId, coupon_code: $scope.couponOpt.code, type: 'schedule'})
				.then(function successCallback(response) {
					$scope.dataLoading = false;
					if (response.data.status == '404') {
						$confirm({
							text: response.data.msg ?? 'Invalid coupon',
							title: 'Error',
							ok: 'Ok',
							// cancel: 'No'
						})
					} else {
						$scope.discounts = response.data
					}
				});
		}

		$scope.checkPrint = function () {
			$scope.enablePrint = false;
			if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				(
					$scope.activeResourceDetails.learning_result.completion_status === 'completed' ||
					$scope.activeResourceDetails.learning_result.completion_status === ('%%event_completion_state_completed%%').toLowerCase()
				) &&
				(
					(
						!$scope.activeResourceDetails.learning_result.sign_off_trainee &&
						!$scope.activeResourceDetails.learning_result.sign_off_manager
					) ||
					(
						$scope.activeResourceDetails.learning_result.sign_off_trainee &&
						$scope.activeResourceDetails.learning_result.sign_off_manager
					) ||
					$scope.checkType('h5p') // Also enable print for h5p, regardless of sign off state
				) &&
				!$scope.checkType('upload') &&
				!$scope.activeResourceDetails.learning_result.survey
			) {
				$scope.enablePrint = true;
			}
			if ($scope.checkType('reflective_log')) {
				$scope.enablePrint = false;
			}

			return $scope.enablePrint;
		};

		$scope.enrollCurrentResource = function () {
			if (
				$scope.getResourceProperty('schedule_id') &&
				$scope.activeResource.type &&
				$scope.activeResource.type === 'lesson'
			) {
				if($scope.activeResource.deadline_at) {
					const deadlineAt = new Date($scope.activeResource.deadline_at);
					$scope.activeResource.deadlineDate = deadlineAt;
				}
				if($scope.activeResource.drop_off_deadline_at) {
					const dropOffDeadlineAt = new Date($scope.activeResource.drop_off_deadline_at);
					$scope.activeResource.dropOffDeadlineAt = dropOffDeadlineAt;
				}
				const startsAt = new Date($scope.activeResource.startsAt);
				$scope.activeResource.startsAt = startsAt;
				if($scope.activeResourceDetails.schedule_lesson_links[0].schedule.discounted_cost !== null){
					$scope.activeResource.discounted_cost = $scope.activeResourceDetails.schedule_lesson_links[0].schedule.discounted_cost
				}
				$uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-enrole_any_learner.html?v=<?=$version?>',
					controller: 'ModalEnrolAnyLearner',
					size: 'lg',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
								calendarEvent: $scope.activeResource
							};
						}
					}
				});
			} else {
				if ($scope.activeResourceDetails?.discounted_cost !== null) {
					$scope.activeResource.discounted_cost = $scope.activeResourceDetails.discounted_cost
				}
				$uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-enrole-learning_resource.html?v=<?=$version?>',
					controller: 'ModalEnrolLearningModule',
					size: 'lg',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
                                module: $scope.activeResource.module ? $scope.activeResource.module : $scope.activeResource,
                                activeResourceDetails:$scope.activeResourceDetails,
                                is_retake: $scope.activeResourceDetails.is_retake ? $scope.activeResourceDetails.is_retake : false,
                                retake_fee: $scope.activeResourceDetails.retake_fee ? $scope.activeResourceDetails.retake_fee : 0,
							};
						}
					}
				});
			}
		};

		$scope.makePayment = function (event) {
			var type = $scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links?.length > 0 ? "schedules" : "learning_modules";
			/*Civica Payments Engine Enable check. Enabling this option will allow learning to be purchased from the system */
			if (
				(
					$rootScope.config.isCivicaPaymentsEngine ||
					$rootScope.config.isGlobalPaymentsEngine ||
					$rootScope.config.enablePay360 ||
					$rootScope.config.enableStripePayment
				) &&
				event.cost > 0
			) {
				if ($rootScope.config.isCivicaPaymentsEngine) {
					if ($scope.couponOpt.enabled && $scope.couponOpt.code !== '') {
						$scope.paymentGatewayService.civicaPayment(event.id, type, null, null, $scope.couponOpt.code);
					} else {
						$scope.paymentGatewayService.civicaPayment(event.id, type);
					}
				}else if($rootScope.config.enableStripePayment){
					if ($scope.couponOpt.enabled && $scope.couponOpt.code !== '') {
							$stripeService.pay(type,event.id,event,$scope.couponOpt.code);
					} else {
							$stripeService.pay(type,event.id,event);
					}
				} else if ($rootScope.config.enablePay360) {
					$pay360Service.pay(
						event.id, type, null, null,
						$scope.couponOpt.enabled && $scope.couponOpt.code !== "" ? $scope.couponOpt.code : undefined,
                        event
					);
				} else {
					let pattern = /learner\/resources\/[\w-]+/;
					match = location.href.match(pattern);
					event.link = match[0];
					$globalPaymentService.makePayment('schedules',event.id,event);

					if($scope.couponOpt.enabled && $scope.couponOpt.code!==''){
						$globalPaymentService.makePayment('schedules',event.id,event,$scope.couponOpt.code);
					}else{
						$globalPaymentService.makePayment('schedules',event.id,event);
					}
				}
			}
		};

		// climb the property hell to module type, if exists
		$scope.checkType = function (slug, is_custom) {
			var response = false;
			if (
				(
					slug &&
					$scope.activeResource &&
					$scope.activeResource.module &&
					$scope.activeResource.module.type &&
					$scope.activeResource.module.type.slug === slug
				) ||
				(
					is_custom &&
					$scope.activeResource &&
					$scope.activeResource.module &&
					$scope.activeResource.module.type &&
					$scope.activeResource.module.type.custom
				)
			) {
				response = true;
			}
			return response;
		};

		// check if Turnitin assignment
		$scope.isTurntItIn = function () {
			try {
				return $scope.activeResourceDetails.learning_result.module.evidence_type.name.toLowerCase().includes("turnitin");
			} catch (e) {
				return false;
			}
		};

		$scope.createdByUser = function () {
			if (
				$scope.activeResource &&
				$scope.activeResource.module &&
				$rootScope.currentUser &&
				$scope.activeResource.module.created_by === $rootScope.currentUser.id
			) {
				return true;
			}
		};

		// Modified Scorm function to open resource inline in iframe

		$scope.disableVideoClicks = $rootScope.config.disableVideoClicks;

		$scope.launch = {
			popup: false,
			type: '',
			isPlayingVideo: false,
			isLoadingVideo: false,
			moodle_course_id: undefined,
			moodle_learning_result_id: undefined,
			playerClosing: false,
			jackdaw: false,
			askPlayerToClose: () =>
			{
				if ($scope.go1_id)
				{
					$scope.go1_id = null;
					$scope.launch.playerClosing = true;
					$timeout($scope.launch.askPlayerToClose, 2000);
					return;
				}

				// announce in subiframes to save data, then close player
				var iframeWin;

				$scope.launch.playerClosing = true;
				try {
					iframeWin = document.querySelector('#launch-popup').contentWindow.document.querySelector('#scoframe1');
				} catch (e) {
					$scope.launch.closePopup();
				}
				if (
					iframeWin &&
					iframeWin.contentWindow &&
					iframeWin.contentWindow.SCOFinish
				) {
					iframeWin.contentWindow.postMessage('close-player', '*');
					// OR I can do this:
					/*
					iframeWin.contentWindow.SCOFinish();
					iframeWin.contentWindow.closeRoom();
					*/
				} else if (
					iframeWin &&
					iframeWin.contentWindow &&
					iframeWin.contentWindow.Unload
				) {
					iframeWin.contentWindow.Unload();
					$scope.launch.closePopup();
				} else {
					$scope.launch.closePopup();
				}
			},
			closePopup: function (force) { // was fired multiple times, might be done by flash calling to parent
				if (
					$scope.launch.popup ||
					force
				) { // What a clever workaround, round of applause, oscar right here!
					$scope.launch.popupInProgress();
					$scope.launch.popup = false;
					$rootScope.hiddenBodyScroll = false;
					$scope.launch.isPlayingVideo = false;
					$scope.launch.isLoadingVideo = false;
					$scope.launch.url = $sce.trustAsResourceUrl('<?=$LMSUri?>blank.html?v=<?=$version?>');
					if ($scope.launch.type === 'moodle') {
						$http.get(
							"<?=$LMSUri?>mylearning/moodle/update/" +
							$scope.launch.moodle_learning_result_id +
							"/" +
							$scope.launch.moodle_course_id
						);
					}
					// Get resource list in a second!
					$timeout(function () {
						$learnerOperations.getResourceList(true);
						$scope.launch.playerClosing = false;

						if (
							$scope.launchedResource &&
							$rootScope.config.askForFeedbackOnComplete
						) {
							$http.get("<?=$LMSUri?>learning/learning-result/completion-status/"+$scope.launchedResource.id).then(function successCallback(response)
								{
									if (response.data && response.data.completion_status === 'completed') {
										$feedbackService.open($scope.launchedResource.module);
									}
									$scope.launchedResource = null;
								}
							);
						}

					}, 1000);
				}
			},
			popupInProgress: function () {
				$ngConfirm({
					title: 'Data is being updated, please wait...',
					contentUrl: '<?=$LMSTplsUriHTML?>progress-bar.html?v=<?=$version?>',
					closeIcon: false,
					backgroundDismiss: false,
					backgroundDismissAnimation: 'glow',
					onScopeReady: function (scope) {
						var self = this;
						$scope.$on('active-resource-set', function () {
							self.close();
						});

						$timeout(function () {
							self.close();
						}, 5000);
					}
				});
			},
			playVideo: function () { // was fired multiple times, might be done by flash calling to parent
				if ($scope.launch.popup) { // What a clever workaround, round of applause, oscar right here!
					if (angular.element('#launch-popup')[0].contentWindow.player.playVideo) {
						angular.element('#launch-popup')[0].contentWindow.player.playVideo();//youtube
						$scope.launch.isPlayingVideo = true;
					}
					if (angular.element('#launch-popup')[0].contentWindow.player.play) {	//vimeo
						$scope.launch.isLoadingVideo = true;
						angular.element('#launch-popup')[0].contentWindow.player.play().then(function () {
							$scope.launch.isPlayingVideo = true;
							$scope.launch.isLoadingVideo = false;
							$scope.$apply();
						});
					}
				}
			},
			pauseVideo: function () { // was fired multiple times, might be done by flash calling to parent
				if ($scope.launch.popup) { // What a clever workaround, round of applause, oscar right here!

					if (angular.element('#launch-popup')[0].contentWindow.player.pauseVideo) {//youtube
						angular.element('#launch-popup')[0].contentWindow.player.pauseVideo();
						$scope.launch.isPlayingVideo = false;
					}

					if (angular.element('#launch-popup')[0].contentWindow.player.pause) {//vimeo
						angular.element('#launch-popup')[0].contentWindow.player.pause().then(function () {
							$scope.launch.isPlayingVideo = false;
							$scope.launch.isLoadingVideo = false;
							$scope.$apply();
						});
					}
				}
			},
			scorm: function (module) {
				if (!module.scorm_popup) {
					$rootScope.hiddenBodyScroll = true;
					$scope.launch.type = 'scorm';
				}
				$scope.launch.jackdaw = module.jackdaw_resource || module.jackdaw;

				$http({
					method: 'GET',
					url: "<?=$LMSUri?>mylearning/launchscorm/" + module.id
				}).then(function successCallback(response) {
					var scorm_data = response.data,
						launch_url = '<?=$LMSUri?>scorm/play_scorm.php?a=' + scorm_data.scorm.id +
						'&scoid=' + scorm_data.scorm.start_module_id +
						'&fname=' + scorm_data.user.fname +
						'&lname=' + scorm_data.user.lname +
						'&email=' + scorm_data.user.email +
						'&username=' + scorm_data.user.username +
						'&user_id=' + scorm_data.user.id
					;

					$scope.go1_id = scorm_data.scorm.go1_id;

					if (module.scorm_popup) {
						$scope.launch.scorm_popup_window = window.open(launch_url, "_blank", "toolbar=no,location=no,scrollbars=no,resizable=yes,top=" + ((screen.height - scorm_data.scorm.height) / 4) + ",left=" + ((screen.width - scorm_data.scorm.width) / 2) + ",width=" + scorm_data.scorm.width + ",height=" + scorm_data.scorm.height + "");
						$scope.scorm_popup_window_closing = false;
						$scope.launch.scorm_popup_check_timer = setInterval($scope.launch.scorm_popup_check, 500);
					} else {
						$scope.launch.popup = true;
						$scope.launch.width = scorm_data.scorm.width;
						$scope.launch.height = scorm_data.scorm.height;
						if (module.scorm_full_screen) {
							$scope.launch.type = $scope.launch.type + ' full-screen';
						}
						$scope.launch.url = $sce.trustAsResourceUrl(launch_url);
					}


				});
			},
			scorm_popup_check: function () {
				if (
					(
						(
							$scope.launch.scorm_popup_window &&
							$scope.launch.scorm_popup_window.closed
						) ||
						!$scope.launch.scorm_popup_window
					) &&
					!$scope.scorm_popup_window_closing
				) {
					$scope.scorm_popup_window_closing = true;
					clearInterval($scope.launch.scorm_popup_check_timer);
					$scope.launch.closePopup(true);
				}
			},
			video: function (module) {
				$rootScope.hiddenBodyScroll = true;
				$http({
					method: 'GET',
					url: "<?=$LMSUri?>mylearning/launchvideo/" + module.id
				}).then(function successCallback(response) {
					var learning_data = response.data;
					$scope.launch.popup = true;
					$scope.launch.type = learning_data.video_type;
					$scope.launch.url = $sce.trustAsResourceUrl(
						'<?=$LMSUri?>tincan/play_tincan.php?course_id=' + learning_data.id +
						'&video_id=' + learning_data.video_id +
						'&video_url=' + learning_data.material.link + // in case of private URL's video id is incorrect, give full url to embed request to vimeo.
						'&video_type=' + learning_data.video_type +
						'&fname=' + learning_data.user.fname +
						'&lname=' + learning_data.user.lname +
						'&email=' + learning_data.user.email +
						'&username=' + learning_data.user.username +
						'&user_id=' + learning_data.user.id +
						'&controls=' + (
							(
								learning_data.video_type === "youtube"
							) &&
								(
									$scope.disableVideoClicks
								) ?
								0 : 1
						)
					);
				});
			},
			moodle: function (resource) {
				$rootScope.hiddenBodyScroll = true;
				$scope.launch.type = 'moodle';
				$scope.launch.popup = true;
				$scope.launch.moodle_course_id = resource.learning_result.module.material.moodle_course_id;
				$scope.launch.moodle_learning_result_id = resource.learning_result.id;
				$scope.launch.url = $sce.trustAsResourceUrl(
					$rootScope.config.moodleLink + '/course/view.php?id=' + $scope.launch.moodle_course_id
				);
			},
			turnitin: function (module_id) {
				$rootScope.hiddenBodyScroll = true;
				$scope.launch.type = 'turnitin';
				$scope.launch.popup = true;
				$scope.launch.url = $sce.trustAsResourceUrl(
					"<?=$LMSUri?>turnitin/trainee/" + module_id
				);
			},
			h5p: function (module_id) {
				$rootScope.hiddenBodyScroll = true;
				$scope.launch.type = 'h5p';
				$scope.launch.popup = true;
				$scope.launch.url = $sce.trustAsResourceUrl(
					"<?=$LMSUri?>h5p/trainee/" + module_id
				);
			},
			printCurrentScreen: function () {
				document.getElementById("launch-popup").contentWindow.print();
			}
		};

		// This will be called from iframe in iframe for e-learning resource types.
		window.closeIframeInline = function () { // redundant now.
			$scope.launch.closePopup();
		};
		window.close = function () {
			$scope.launch.closePopup();
		};

		// Launch from lesson
		// Launch from lesson
		$scope.launchLessonResource = function (resource) {
			$scope.launchedResource = {
				id: resource.learning_result.id,
				module: {
					id: resource.id,
					name: resource.name,
				},
			};
			switch (resource.type.slug) {
				case 'e_learning':
					if (
						(
							// Condition 1: The resource is 'completed'
							resource.learning_result.completion_status === 'completed' ||
							// OR Condition 2: The resource is 'failed' AND has no attempts left
							(
								resource.learning_result.passing_status === 'failed' &&
								resource.learning_result.attempts_left < 1
							)
						) &&
						// AND refresh learning is allowed
						$rootScope.config.allowLearnerRefreshLearning
					) {
						$scope.recordNewLearningEvent(resource);
					} else if (resource.learning_result.passing_status === 'failed') {
						// This will now only catch cases where the resource is 'failed' but still has attempts left
						$scope.reAttemptLearning(resource);
					} else {
						if (
							resource?.version &&
							resource.learning_result?.completion_status === 'completed' &&
							resource?.learning_result?.completed_version != resource?.version
						) {
							$ngConfirm({
								title: '%%learner__learning_reset__title%%',
								content: '%%learner__completed_version_mismatch__warning%%',
								columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
								backgroundDismiss: true,
								onOpen: function() {
									var self = this;
									var $dialog = self.$el;
									var $buttons = $dialog.find('.ng-confirm-buttons button');
									if ($buttons.length > 0) $buttons.first().focus();
									else $dialog.focus();
									var focusableElementsSelector = 'a[href], area[href], input:not([disabled]):not([type="hidden"]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, [tabindex]:not([tabindex="-1"])';
									var $focusableInDialog = $dialog.find(focusableElementsSelector);
									var $firstFocusable = $focusableInDialog.first();
									var $lastFocusable = $focusableInDialog.last();
									self.focusTrapHandler = function(e) {
										if (e.key === 'Tab' || e.keyCode === 9) {
											if (e.shiftKey) { // Shift + Tab
												if (document.activeElement === $firstFocusable[0]) {
													e.preventDefault();
													$lastFocusable.focus();
												}
											} else { // Tab
												if (document.activeElement === $lastFocusable[0]) {
													e.preventDefault();
													$firstFocusable.focus();
												}
											}
										}
									};
									$dialog.on('keydown.ngConfirmA11y', self.focusTrapHandler);
									self.escapeKeyHandler = function(e) { if (e.key === 'Escape' || e.keyCode === 27) self.close(); };
									$(document).on('keydown.ngConfirmA11y', self.escapeKeyHandler);
								},
								onClose: function() {
									var self = this;
									if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
									if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
								},
								onDestroy: function() {
									var self = this;
									if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
									if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
								},
								buttons: {
									ok: {
										text: '%%learner__learning_reset__accept%%',
										btnClass: 'btn-blue',
										action: function(scope, button){
											return true;
										}
									},
								}
							});
						} else {
							$scope.launch.scorm(resource);
						}
					}
					break;

				case 'youtube':
					$scope.launch.video(resource);
					break;

				case 'vimeo':
					$scope.launch.video(resource);
					break;

				case 'h5p':
					$scope.launch.h5p(resource.id);
					break;

				case 'webpage':
					$window.open($textOperations.urlPrefix(resource.material.link), '_blank');
					break;
			}
		};

		$scope.dismissedRecommendations = [];

		$scope.dismissRecommendation = function () {
			$http.put(
				"<?=$LMSUri?>learning/dismiss-recommendation/" + $scope.activeResourceDetails.learning_result.learning_module_id
			).then(
				function () {
					$scope.dismissedRecommendations.push($scope.activeResourceDetails.learning_result.learning_module_id);
					$scope.activeResourceDetails.is_recommended = false;
					$scope.scrollToResources();
				}
			);
		};

        $scope.showResetModel = function (callback=null) {
            if (
                (
                    // Condition 1: The resource is 'completed'
                    $scope.activeResourceDetails.learning_result.completion_status === 'completed' ||
                    // Condition 2: The resource is 'failed' AND has no attempts left
                    (
                        $scope.activeResourceDetails.learning_result.passing_status === 'failed' &&
                        $scope.activeResourceDetails.learning_result.attempts_left < 1
                    )
                ) &&
                // AND all the other conditions for refresh learning are met
                $scope.activeResourceDetails.learning_result.module.is_course == 0 &&
                $scope.activeResourceDetails.learning_result.module.is_skill == 0 &&
                $scope.activeResourceDetails.learning_result.module.is_skillscan == 0 &&
                $scope.activeResourceDetails.learning_result.module.is_survey == 0 &&
                $rootScope.config.allowLearnerRefreshLearning
            ) {
                // If conditions are met, prompt to record a new learning event
                $scope.recordNewLearningEvent(null,callback);
            } else {
                // Otherwise (e.g., failed with attempts remaining, in progress, etc.), execute the standard launch action
                if(callback){
                    callback();
                }
            }
        };

		//Launch action, either launch resource or show pre-requisites
		$scope.launchResource = function ($event) {
			$event.preventDefault();
			if ($scope.activeResource.expiration_at && $scope.activeResource.expiration_at < new Date().toISOString()) {
				$confirm({
					text: 'Access to this Learning Resource has expired. Select the Details link below for further information.',
					title: 'Resource Expired',
					cancel: 'Okay'
				}).then(function () {
				});
				return;
			}
			if($scope.activeResource.learning_module_id)
			{
				$rootScope.las.trackLearningModule($scope.activeResource.learning_module_id);
			}
			if ($scope.activeResource.completion_status !== 'completed') $scope.launchedResource = $scope.activeResource;
			
			/** 
			 * check whether the resource is a SCORM module that needs to be launched in a pop-up window.
			 * If so, show a warning to inform the user that it cannot play in the 
			 */
			if ($scope.hideOtherResources && $scope?.activeResourceDetails?.learning_result?.module?.scorm_popup) {
				$confirm({
					text: 'This course is ran in a pop-up window and cannot be run through this app, you need to use the web interface instead.',
					title: 'Unable to play course',
				}).then(function () {
				});
				return;
			}

			if ($scope.activeResourceDetails?.learning_result?.module?.user_forms?.length > 0) {
				$click_from='learner_home';
				$scope.ss.viewForm($scope.activeResourceDetails.learning_result.module.user_forms[0],
					$rootScope.currentUser.id,$click_from)
			} else if ($scope.activeResourceDetails?.learning_result?.module?.user_programme_forms?.length > 0) {
				$click_from='learner_home';
				$scope.ss.viewForm($scope.activeResourceDetails.learning_result.module.user_programme_forms[0],
							$rootScope.currentUser.id,$click_from)
			} else {
				if ($scope.activeResource.assigned === 0) {
					$scope.enrollCurrentResource();
				} else if (
					$scope.uncompletedPrerequisites &&
					!$scope.activeResource.module.is_course
				) {
					$scope.setActiveTab($scope.resourceTabs[2]);
				} else if ($scope.uncompletedRequiredModules) {
					$scope.setActiveTab($scope.resourceTabs[$scope.requiredModuleTab]);
				} else if ($scope.checkType('e_learning')) {
					// If resource is completed and "allowLearnerRefreshLearning" configuration option is true, show prompt "This learning has already been completed. Do you wish to record a new learning event (i.e. in the case of refresher training)?".
					if (
						(
							!$scope.activeResourceDetails?.learning_result?.pending_assessment &&
							!$scope.activeResourceDetails?.learning_result?.module?.is_skillscan
						) ||
						(
							$scope.activeResourceDetails?.learning_result?.module?.is_skillscan &&
							$scope.activeResourceDetails?.learning_result?.completion_status !== 'completed'
						)
					) {
						$scope.showResetModel(function () {
							if ($scope.activeResourceDetails.learning_result.passing_status === 'failed') {
								$scope.reAttemptLearning();
							} else if (
								$scope?.activeResourceDetails?.learning_result?.module?.version &&
								$scope.activeResourceDetails?.learning_result?.completion_status === 'completed' &&
								$scope?.activeResourceDetails?.learning_result?.completed_version != $scope?.activeResourceDetails?.learning_result?.module?.version
							) {
								$ngConfirm({
									title: '%%learner__learning_reset__title%%',
									content: '%%learner__completed_version_mismatch__warning%%',
									columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
									backgroundDismiss: true,
									onOpen: function() {
										var self = this;
										var $dialog = self.$el;
										var $buttons = $dialog.find('.ng-confirm-buttons button');
										if ($buttons.length > 0) $buttons.first().focus();
										else $dialog.focus();
										var focusableElementsSelector = 'a[href], area[href], input:not([disabled]):not([type="hidden"]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, [tabindex]:not([tabindex="-1"])';
										var $focusableInDialog = $dialog.find(focusableElementsSelector);
										var $firstFocusable = $focusableInDialog.first();
										var $lastFocusable = $focusableInDialog.last();
										self.focusTrapHandler = function(e) {
											if (e.key === 'Tab' || e.keyCode === 9) {
												if (e.shiftKey) { // Shift + Tab
													if (document.activeElement === $firstFocusable[0]) {
														e.preventDefault();
														$lastFocusable.focus();
													}
												} else { // Tab
													if (document.activeElement === $lastFocusable[0]) {
														e.preventDefault();
														$firstFocusable.focus();
													}
												}
											}
										};
										$dialog.on('keydown.ngConfirmA11y', self.focusTrapHandler);
										self.escapeKeyHandler = function(e) { if (e.key === 'Escape' || e.keyCode === 27) self.close(); };
										$(document).on('keydown.ngConfirmA11y', self.escapeKeyHandler);
									},
									onClose: function() {
										var self = this;
										if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
										if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
									},
									onDestroy: function() {
										var self = this;
										if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
										if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
									},
									buttons: {
										ok: {
											text: '%%learner__learning_reset__accept%%',
											btnClass: 'btn-blue',
											action: function(scope, button){
												return true;
											}
										},
									}
								});
							} else {
								$scope.launch.scorm($scope.activeResourceDetails.learning_result.module);
							}
						});
					}
					//launchScorm($scope.activeResourceDetails.learning_result.module, $http);
				} else if ($scope.checkType('youtube')) {
                    $scope.showResetModel(function () {
					    $scope.launch.video($scope.activeResourceDetails.learning_result.module);
                    });
				} else if ($scope.checkType('classroom') || $scope.checkType('on_the_job')) {
                    $scope.showResetModel(function () {
					    $scope.setActiveTab($scope.resourceTabs[$scope.bookLearningModuleTab]);
                    });
				} else if ($scope.checkType('upload') && !$scope.isTurntItIn()) {
                    $scope.showResetModel(function () {
					// If completed, lead to details section
					if ($scope.activeResourceDetails.learning_result.completion_status === 'completed') {
						$scope.setActiveTab($scope.resourceTabs[1]);
					} else {
						// Launch modal window
						$scope.us.openUpload($scope.activeResourceDetails);
					}
                    });
				} else if ($scope.checkType('form')) {
					// Launch modal window
					$scope.us.openUpload($scope.activeResourceDetails);
				}
				else if ($scope.checkType('blog_entry')) {
					$scope.currentType = 'blog'; // something to prevent JS Lint showing warning
				} else if ($scope.checkType('reflective_log')) {
                    $scope.showResetModel(function () {
					    $scope.us.openUpload($scope.activeResourceDetails);
                    });
				} else if ($scope.checkType('vimeo')) {
                    $scope.showResetModel(function () {
					    $scope.launch.video($scope.activeResourceDetails.learning_result.module);
                    });
					//$rootScope.uploadResources($scope.activeResourceDetails);
				} else if ($scope.checkType('moodle_course')) {
                    $scope.showResetModel(function () {
					    $scope.launch.moodle($scope.activeResourceDetails);
                    });
				} else if ($scope.checkType('turnitin_assignment') || $scope.isTurntItIn()) {
                    $scope.showResetModel(function () {
					$scope.launch.turnitin(
						$scope.activeResourceDetails.learning_result.module.id
					);
                        });
				} else if ($scope.checkType('h5p')) {
                $scope.showResetModel(function () {
					$scope.launch.h5p(
						$scope.activeResourceDetails.learning_result.module.id
					);
                    });
				} else if ($scope.activeResource.module.is_course) {
                    $scope.showResetModel(function () {
					// If lesson is added by event/schedule and contains zoom/teams meeting, launch modal window with list of all resources that can be played inline, else as usual
					if ($scope.activeResourceDetails.instructor_lead) {
						$scope.openInstructorLeadLesson($scope.activeResourceDetails);
					} else {
						$scope.setActiveTab($scope.resourceTabs[2]);
					}
                        });
				} else if ($scope.checkType(false, true)) {
					$scope.us.openUpload($scope.activeResourceDetails);
				} else if ($scope.checkType('webpage')) {
                    $scope.showResetModel(function () {
					if ($scope.activeResourceDetails.learning_result.completion_status === 'not attempted' && $scope.activeResourceDetails.learning_result.module.track_progress) {
						$http({
							method: 'PUT',
							url: '<?=$LMSUri?>learning/learning-result/update-completion-status/' + $scope.activeResourceDetails.learning_result.id,
							data: {
								completion_status: 'in progress'
							}
						}).then(function successCallback() {
							$learnerOperations.getResourceList(true);
						});
					}
                     let url = $scope.launchResourceLink;
                    let target = $scope.launchResourceTarget;
                    window.open(url, target);
                    });
				}
			}
		};

		$scope.openInstructorLeadLesson = function (lesson) {
			$uibModal.open({
				animation: true,
				ariaLabelledBy: 'modal-title',
				ariaDescribedBy: 'modal-body',
				templateUrl: '<?=$LMSTplsUriHTML?>modal-instructor-lead-lesson.html?v=<?=$version?>',
				controller: 'modalInstructorLeadLesson',
				size: 'lg',
				windowClass: 'u-modal--full-width',
				backdrop: 'static',
				resolve: {
					data: function () {
						return lesson;
					}
				}
			});
		};

		// In case evidence resource is updated, update current open evidence with correct data.
		$scope.$on('learner-evidence-updated', function (event, args) {
			$scope.updateLearnerEvidenceEvent = event;
			$scope.updateLearnerEvidenceArgs = args;
			$scope.setActiveResourceDetails();
		});

		// Instructor lead lesson participat needs to update status
		$scope.$on('refresh-details', function () {
			$scope.setActiveResourceDetails();
		});

		$scope.reAttemptLearning = (resource = null) => {
			if (!resource) resource = $scope.activeResourceDetails;

			if (resource.learning_result.attempts_left > 0) $ngConfirm({
				title: 'You have not achieved the required passing grade for this test.',
				content: 'Please click the blue <b>New Attempt</b> button below to start the learning again. You have <b>' + (resource.learning_result.attempts_left == 99 ? "unlimited" : resource.learning_result.attempts_left) + '</b> attempt(s) left.',
				columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
				backgroundDismiss: true,
				buttons: {
					ok: {
						text: 'Make a New Attempt',
						btnClass: 'btn-blue',
						action: function(scope, button) {
							$scope.$apply(function() {
								$http({
									method: 'GET',
									url: '<?=$LMSUri?>learning/re-attempt-learning/' + (resource?.learning_result?.module?.id || resource?.learning_result?.learning_module_id)
								}).then(function successCallback(res) {
                                    if(res?.data?.retake_fee) {
                                        $scope.activeResourceDetails.is_retake = true;
                                        $scope.activeResourceDetails.retake_fee = res.data.retake_fee;
                                        $scope.retake();
                                    }else{
									  window.location.reload(false);
                                    }
								}, function errorCallback() {
									$window.alert('New Learning Event can not be recorded, please contact your manager.');
								});
							});
							return true;
						}
					},
					existing: {
						text: 'Access Existing Record',
						btnClass: 'btn-default',
						action: function(scope, button) {
							$scope.$apply(function() {
								$scope.launch.scorm(resource?.learning_result?.module || resource);
							});
							return true;
						}
					},
				}
			});
		}

		$scope.recordNewLearningEvent = function (resource = null, callback) {
			var module_id = $scope?.activeResourceDetails?.learning_result?.module?.id,
				module = $scope?.activeResourceDetails?.learning_result?.module,
				completed_version = $scope?.activeResourceDetails?.learning_result?.completed_version,
				version = $scope?.activeResourceDetails?.learning_result?.module?.version;

			if (resource) {
				module_id = resource.id;
				module = resource;
				version = resource.version;
				completed_version = resource.learning_result.completed_version;
			}

			if (completed_version != version) {
				$ngConfirm({
					title: '%%learner__learning_reset__title%%',
					content: '%%learner__completed_version_mismatch__confirm%%',
					columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
					backgroundDismiss: true,
					onOpen: function() {
						var self = this;
						var $dialog = self.$el;
						var $buttons = $dialog.find('.ng-confirm-buttons button');
						if ($buttons.length > 0) $buttons.first().focus();
						else $dialog.focus();
						var focusableElementsSelector = 'a[href], area[href], input:not([disabled]):not([type="hidden"]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, [tabindex]:not([tabindex="-1"])';
						var $focusableInDialog = $dialog.find(focusableElementsSelector);
						var $firstFocusable = $focusableInDialog.first();
						var $lastFocusable = $focusableInDialog.last();
						self.focusTrapHandler = function(e) {
							if (e.key === 'Tab' || e.keyCode === 9) {
								if (e.shiftKey) { // Shift + Tab
									if (document.activeElement === $firstFocusable[0]) {
										e.preventDefault();
										$lastFocusable.focus();
									}
								} else { // Tab
									if (document.activeElement === $lastFocusable[0]) {
										e.preventDefault();
										$firstFocusable.focus();
									}
								}
							}
						};
						$dialog.on('keydown.ngConfirmA11y', self.focusTrapHandler);
						self.escapeKeyHandler = function(e) { if (e.key === 'Escape' || e.keyCode === 27) self.close(); };
						$(document).on('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					onClose: function() {
						var self = this;
						if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
						if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					onDestroy: function() {
						var self = this;
						if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
						if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					buttons: {
						ok: {
							text: '%%learner__learning_reset__confirm%%',
							btnClass: 'btn-blue',
							action: function(scope, button){
								$scope.$apply(function() {
									$http({
										method: 'GET',
										url: '<?=$LMSUri?>learning/record-new-learning/' + module_id
									}).then(function successCallback() {
										window.location.reload(false);
									}, function errorCallback() {
										$window.alert('New Learning Event can not be recorded, please contact your manager.');
									});
								});
								return true;
							}
						},
						cancel: {
							text: '%%learner__learning_reset__cancel%%',
							btnClass: 'btn-default',
							action: function(scope, button){
								return true;
							}
						},
					}
				});
			} else {
				$ngConfirm({
					title: '%%learner__learning_reset__title%%',
					content: '%%learner__learning_reset__description%%',
					columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
					backgroundDismiss: true,
					onOpen: function() {
						var self = this;
						var $dialog = self.$el;
						var $buttons = $dialog.find('.ng-confirm-buttons button');
						if ($buttons.length > 0) $buttons.first().focus();
						else $dialog.focus();
						var focusableElementsSelector = 'a[href], area[href], input:not([disabled]):not([type="hidden"]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, [tabindex]:not([tabindex="-1"])';
						var $focusableInDialog = $dialog.find(focusableElementsSelector);
						var $firstFocusable = $focusableInDialog.first();
						var $lastFocusable = $focusableInDialog.last();
						self.focusTrapHandler = function(e) {
							if (e.key === 'Tab' || e.keyCode === 9) {
								if (e.shiftKey) { // Shift + Tab
									if (document.activeElement === $firstFocusable[0]) {
										e.preventDefault();
										$lastFocusable.focus();
									}
								} else { // Tab
									if (document.activeElement === $lastFocusable[0]) {
										e.preventDefault();
										$firstFocusable.focus();
									}
								}
							}
						};
						$dialog.on('keydown.ngConfirmA11y', self.focusTrapHandler);
						self.escapeKeyHandler = function(e) { if (e.key === 'Escape' || e.keyCode === 27) self.close(); };
						$(document).on('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					onClose: function() {
						var self = this;
						if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
						if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					onDestroy: function() {
						var self = this;
						if (self.$el && self.focusTrapHandler) self.$el.off('keydown.ngConfirmA11y', self.focusTrapHandler);
						if (self.escapeKeyHandler) $(document).off('keydown.ngConfirmA11y', self.escapeKeyHandler);
					},
					buttons: {
						ok: {
							text: '%%learner__learning_reset__confirm%%',
							btnClass: 'btn-blue',
							action: function(scope, button){
								$scope.$apply(function() {
									$http({
										method: 'GET',
										url: '<?=$LMSUri?>learning/record-new-learning/' + module_id
									}).then(function successCallback() {
										window.location.reload(false);
									}, function errorCallback() {
										$window.alert('New Learning Event can not be recorded, please contact your manager.');
									});
								});
								return true;
							}
						},
						existing: {
							text: '%%learner__learning_reset__existing%%',
							btnClass: 'btn-default',
							action: function(scope, button){
								$scope.$apply(function() {
                                    if(callback){
									    callback();
                                    }else{
                                        $scope.launch.scorm(module);
                                    }
								});
								return true;
							}
						},
					}
				});
			}
		};

		// change "launch resource" button text/link/target, depending on conditions
		$scope.hideLaunchButton = true;
		$scope.launchResourceSet = function () {
			$scope.launchResourceText = $rootScope.config.launchResourceText || 'Launch resource';
			// If distributor, change label of button
			if ($rootScope.rs.isDistributor()) {
				$scope.launchResourceText = 'Preview';
			}
			$scope.launchResourceLink = '#';
			$scope.launchResourceTarget = '';
			$scope.disabledText = false;
			$scope.hideLaunchButton = false;

			// This resource was assigned, is completed, not assigned any more, do not allow to launch
			if (
				$rootScope.config.showUnassignedDisabledLearning &&
				$scope?.activeResource?.id > 0 &&
				(
					$scope?.activeResource?.user_learning_modules_id === null ||
					$scope?.activeResource?.module?.status === false
				)
			) {
				$scope.hideLaunchButton = true;
			}

			if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.expiration_date &&
				$scope.activeResourceDetails.learning_result.module.expired === 1
			) {
				$scope.hideLaunchButton = true;
				$scope.disabledText = 'This asset is no longer accessible - contact your system administrator for further information.';
			} else if ($scope.activeResource.assigned === 0) {
				// If event not assigned and waiting for approval, disble text
				if (
					$scope.activeResource.waiting &&
					$scope.activeResource.waiting[0] &&
					$scope.activeResource.waiting[0].id === $rootScope.currentUser.id
				) {
					$scope.hideLaunchButton = true;
				}else if($scope.activeResourceDetails?.schedule_lesson_links[0]?.schedule?.waiting ){
                        $scope.hideLaunchButton = true;
                    }
                    else {
					$scope.launchResourceText = '%%launch_resource_text_enrol%%';
				}

				// Hide enroll button if resource/lesson/event is in process of assignment
				if ($scope.events.showCancel()) {
					$scope.hideLaunchButton = true;
				}

				if ($rootScope.isSMCR) {
					$scope.launchResourceText = 'Add to F&P Activities File';
				}
			} else if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.is_skillscan &&
				$scope.resourceStatuss('completed')
			) {
				$scope.launchResourceText = 'Skill Scan Results';
				$scope.launchResourceLink = "<?=$LMSAppUri?>skill-scan/" + $scope.activeResourceDetails.learning_result.id;
			} else if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.pending_assessment &&
				!$scope.activeResourceDetails.learning_result.module.is_skillscan
				&& !$scope.activeResourceDetails.learning_result.survey
			) {
				$scope.launchResourceText = 'Pending Assessments';
				$scope.launchResourceLink = "<?=$LMSAppUri?>myassessment/" + $scope.activeResourceDetails.learning_result.pending_assessment.id;
			} else if (
				$scope.uncompletedPrerequisites &&
				!$scope.activeResource.module.is_course
			) {
				$scope.launchResourceText = 'View Pre-requisites';
			} else if (
				$scope.checkType('webpage') ||
				$scope.checkType('zoom_meeting') ||
				$scope.checkType('microsoft_teams') ||
				$scope.checkType('google_classroom')
			) {
				$scope.launchResourceText = 'Open link in new window';
				if ($scope.checkType('webpage')) {
					$scope.launchResourceText = '%%resource_type__webpage_launch_button%%';
				}
				$scope.launchResourceLink = $textOperations.urlPrefix($scope.activeResourceDetails.learning_result.module.material.link);
				$scope.launchResourceTarget = '_blank';
			} else if (
				$scope.checkType('classroom') ||
				$scope.checkType('on_the_job')
			) {
				$scope.launchResourceText = 'Booking';
			} else if ($scope.checkType('book_cd_dvd')) {
				if (
					$scope.activeResourceDetails.learning_result.module.material &&
					$scope.activeResourceDetails.learning_result.module.material.link
				) {
					$scope.launchResourceText = 'Open link in new window';
					$scope.launchResourceLink = $textOperations.urlPrefix($scope.activeResourceDetails.learning_result.module.material.link);
					$scope.launchResourceTarget = '_blank';
				} else {
					$scope.hideLaunchButton = true;
					$scope.launchResourceText = 'Read the book!';
				}
			} else if ($scope.uncompletedRequiredModules) {
				//requiredModuleTab
				$scope.launchResourceText = 'View required modules';
			} else if ($scope.checkType('upload') || $scope.checkType('form')) {
				if ($scope.isTurntItIn()) {
					$scope.launchResourceText = "Open Assignment";
				} else {
					$scope.launchResourceText = ($scope.activeResourceDetails.learning_result.module.track_progress ? 'Edit ' : 'View ') + (
						$scope.activeResourceDetails.learning_result &&
							$scope.activeResourceDetails.learning_result.module &&
							$scope.activeResourceDetails.learning_result.module.project ?
							'Project' :
							$rootScope.typeName(7)
					);
				}
				//$scope.launchResourceLink =  "<?=$LMSAppUri?>learner/uploadresources/" + $scope.activeResource.module.id;
				// if completed, hide button
				if ($scope.resourceStatuss('completed')) {
					$scope.launchResourceText = "View Details";
				}
			} else if ($scope.checkType('blog_entry')) {
				if (
					$scope.activeResource.module.material &&
					$scope.activeResource.module.material.link
				) {
					$scope.launchResourceText = 'Open link in new window';
					$scope.launchResourceLink = $textOperations.urlPrefix($scope.activeResource.module.material.link);
					$scope.launchResourceTarget = '_blank';
				} else {
					$scope.hideLaunchButton = true;
				}
			} else if ($scope.checkType('reflective_log')) {
				$scope.launchResourceText = 'Reflective Log';
				// if completed, change name of button
				if ($scope.resourceStatuss('completed')) {
					$scope.launchResourceText = 'View the original comments';
				}
			} else if ($scope.checkType('moodle_course')) {
				$scope.launchResourceText = 'Open Moodle Course';
				// if completed, change name of button
				if ($scope.resourceStatuss('completed')) {
					$scope.launchResourceText = 'View the original comments';
				}
			} else if (
				$scope.activeResource &&
				$scope.activeResource.module &&
				$scope.activeResource.module.is_course
			) {
				if ($scope.activeResourceDetails.instructor_lead) {
					$scope.launchResourceText = 'Launch %%lesson%%';
				} else {
					// if lesson does not have any resource in it, do not show button:
					if (
						$scope.activeResourceDetails?.learning_result?.module?.modules?.length > 0 ||
						$scope.activeResourceDetails?.learning_result?.module?.user_forms?.length > 0 ||
						$scope.activeResourceDetails?.learning_result?.module?.user_programme_forms?.length > 0
					) {
						$scope.launchResourceText = 'Open %%lesson%%';

					} else {
						$scope.launchResourceText = '%%lesson_resources%%';
						$scope.hideLaunchButton = true;
					}
				}
				// Checks if resource is custom type!
			} else if ($scope.checkType(false, true)) {
				$scope.launchResourceText = 'Launch %%learning_resources%%';
			}

			// If resource is to be marked as disabled after completion, hide launch button if resource turns to completed!
			if (
				$scope.activeResource.module &&
				$scope.activeResource.module.disable_upon_completion &&
				$scope.resources.progress() === 'completed'
			) {
				$scope.hideLaunchButton = true;
			}

			if ($scope.activeResource.refreshed_event) {
				$scope.hideLaunchButton = true;
			}

			if (
				$scope.activeResourceDetails?.learning_result?.passing_status === 'failed' &&
				$scope.checkType('e_learning')
			) {
				// Only hide the button if there are no attempts left AND refresh learning is disabled
				if ($scope.activeResourceDetails?.learning_result?.attempts_left < 1 && !$rootScope.config.allowLearnerRefreshLearning) {
					$scope.hideLaunchButton = true;
				} else if ($scope.activeResourceDetails?.learning_result?.attempts_left > 0) {
					$scope.launchResourceText = '%%failed_learning__re_attempt%%';
				}
			}

		};

		$scope.resourceStatuss = function (status) {
			var response = false;
			if (
				$scope.activeResource.completion_status === status
			) {
				response = true;
			}
			return response;
		};

		$scope.launchScorm = function (learningModule) {
			window.windowScope = $rootScope;
			launchScorm(learningModule, $http);
		};

		$scope.launchYoutube = function (learningModule) {
			window.windowScope = $rootScope;
			launchYoutube(learningModule, $http);
		};

		// Find resource using ID.
		$scope.setActiveResource = function (resource, activeResourceKey, schedule_id) {
			$scope.hideResource = false;
			// fail if resource is not given.
			if (!resource) {
				return false;
			}

			// check if resource has property object module un use it's id, if not use resource id, if not use resource itself as ID, so clever!
			var
				i,
				entry,
				id = parseInt((resource.module && resource.module.id) ? resource.module.id : (resource.id || resource), 10),
				route_param
				;

			$scope.resource_param = $routeParams.resource;
			if (
				id &&
				!$scope.resource_param
			) {
				$scope.resource_param = id;
			} else if ( // Will see if this brings problem somewhere.
				$scope.resource_param &&
				id && (
					$scope.resource_param !== id
				)
			) {
				$scope.resource_param = id;
			}

			schedule_id = parseInt(resource.schedule_id || schedule_id, 10);

			// IF schedule_id is passed and is not part of resource_param, add it!
			if (
				schedule_id &&
				$scope.resource_param &&
				isInt($scope.resource_param) ||
				(
					!isInt($scope.resource_param) &&
					$scope.resource_param.indexOf('-' + schedule_id) === -1
				)
			) {
				$scope.resource_param = $scope.resource_param + '-' + schedule_id;
			}

			$scope.activeResourceKey = activeResourceKey; // used in resource list at the bottom of page to differentiate selected resource.

			$scope.resourceSignOffAlerts = []; // Reset sign off alert!

			// loop all resources to find selected by its property value
			if ($scope.resourceListSorted) {
				$scope.resourceFound = false;
				for (i = $scope.resourceListSorted.length - 1; i >= 0; i--) {
					entry = $scope.resourceListSorted[i];
					if (
						entry.module.id === id &&
						(
							!schedule_id ||
							entry.schedule_id === schedule_id
						)
					) {
						route_param = entry.module.id;
						if (entry.schedule_id) {
							route_param = entry.module.id + '-' + entry.schedule_id;
						}
						$scope.activeResource = $scope.resourceListSorted[i];
						$scope.activeResource.module.type_id = parseInt($scope.activeResource.module.type_id, 10);
						$scope.promoImageStyle = "";

						// active resource is set, not details, but initial resource, before detail retrieval.
						$scope.$broadcast('active-resource-set');
						if ($scope.activeResource?.module.name) document.title = $scope.baseTitle + ": " + $scope.activeResource.module.name;
						// request additional details, prerequisites
						$scope.setActiveResourceDetails();

						// If clicked on valid resource and missing resource message still present, hide it.
						$scope.missingResource = false;

						// scroll page to top
						if (window.pageYOffset === 0)
						{
							window.scrollTo(0, 1);
							smoothScroll(document.querySelector('body'));
						}
						else smoothScroll(document.querySelector('body'));

						// if resource is loaded and not set in root param, set it!
						if (!$routeParams.resource && !$rootScope.config.isBrowsebyDefault) {
							$routeParams.resource = route_param; // had to apply this, as empty resource would not let load routes.js
							$location.path('learner/resources/' + route_param + ($routeParams.tab ? '/' + $routeParams.tab : ''));
						}

						$window.localStorage.setItem('learner_last_visited_resouce', id);
						$scope.resourceFound = true;
						break;
					}
				}
				// current selected resource was not found, check last activeresourcedetails contains courses, redirect back to them.
				if (!$scope.resourceFound) {
					// check if activeresource details contain courses, if yes, redirect back to course page
					if ($scope.activeResourceDetails?.learning_result?.module?.courses?.length > 0) {
						$location.path('learner/resources/' + $scope.activeResourceDetails?.learning_result?.module?.courses[0].id);
					}
				}
			}
		};


		// thumbnail logic/check
		$scope.setThumbnail = $learnerOperations.setThumbnail;

		// Reload resource list when jackdaw window is closed
		$scope.$on("resource-list-needs-refresh", function () {
			$scope.resourceListSorted = [];
			$learnerOperations.getResourceList(true);
		});


		// Resource tab buttons under resource overview
		$scope.tabConditions = function () {

			var nextClass = ['', 'first', 'second', 'third', 'fourth'],
				tabLocation = {},
				nextClassCounter = 0;

			$scope.resourceTabs = [];
			$scope.showPrereQuisitePage = false;
			$scope.showLessonInfoPage = false;
			$scope.showBookLearningModulePage = false;
			$scope.showSignOffPage = false;
			$scope.showRequiredModulesPage = false;
			$scope.showEvidenceMeetings = false;

			/*
				Show tabs, depending on specific conditions.
			*/

			//Overview 1
			$scope.resourceTabs.push({ name: 'Overview', class: nextClass[nextClassCounter], url: '' });
			nextClassCounter++;

			/*Restrict items in detail page*/
			if(
				(
					$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links?.length > 0 &&
					$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links[$scope?.activeResource?.schedule_index]?.schedule?.user_link?.approved !== 0 &&
					$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links[$scope?.activeResource?.schedule_index]?.schedule?.user_link?.is_paid !== '0'
				) ||
				(
					$scope?.activeResourceDetails?.learning_result?.approved !== 0 &&
					$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links?.length === 0 &&
					$scope?.activeResourceDetails?.learning_result?.is_paid !== '0'
				)
			){
				$scope.showDetails = true;
			}else{
				$scope.showDetails = false;
			}

			// Details 2
			if (
				$scope.activeResource
			) {
				/*Show for all resources*/
				$scope.resourceTabs.push({ name: 'Details', class: nextClass[nextClassCounter], url: 'details' });
				tabLocation.details = nextClassCounter;
				nextClassCounter++;
			}


			// Booking 3
			if ($scope.checkType('classroom') || $scope.checkType('on_the_job')) {
				$scope.resourceTabs.push({ name: 'Booking', class: nextClass[nextClassCounter], url: 'booklearningmodule' });
				$scope.bookLearningModuleTab = nextClassCounter;
				tabLocation.booklearningmodule = nextClassCounter;
				nextClassCounter++;
				$scope.showBookLearningModulePage = true;
			}

			// Meetings 4
			if (
				$scope.activeResource &&
				$scope.checkType('upload') &&
				$rootScope.config.isMeetings
			) {
				$scope.resourceTabs.push({ name: 'Meetings', class: nextClass[nextClassCounter], url: 'meetings' });
				$scope.showEvidenceMeetings = true;
				tabLocation.meetings = nextClassCounter;
				nextClassCounter++;
			}

			// %%sign_off_button%% 5
			if (
				$scope.activeResource &&
				(
					(
						(
							$scope.checkType('webpage') ||
							$scope.checkType('zoom_meeting') ||
							$scope.checkType('microsoft_teams') ||
							$scope.checkType('google_classroom') ||
							$scope.checkType('classroom') ||
							$scope.checkType('book_cd_dvd') ||
							$scope.checkType('on_the_job') ||
							$scope.checkType('upload') ||
							$scope.checkType('blog_entry') ||
							$scope.checkType('reflective_log') ||
							$scope.checkType('form')
						) &&
						$scope.activeResource.assigned !== 0 &&
						$scope.activeResource.module.track_progress &&
						(
							!$scope.hideLaunchButton &&
							$scope.activeResource &&
							$scope.activeResourceDetails.learning_result.approved !== 0 &&
							$scope.activeResourceDetails?.learning_result?.module?.schedule_lesson_links?.length === 0 &&
							$scope.activeResourceDetails.learning_result.is_paid !== '0'
						)
					) || (
						$scope.activeResourceDetails?.learning_result?.module?.is_skill === 1 &&
						$rootScope.config.isLearnerSkillsSignOff
					)
				)
			) {
				$scope.resourceTabs.push({ name: '%%sign_off_button%%', class: nextClass[nextClassCounter], url: 'signoff' });
				tabLocation.signoff = nextClassCounter;
				nextClassCounter++;
				$scope.showSignOffPage = true;
			}

			if (
				$scope &&
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.prerequisites &&
				$scope.activeResourceDetails.learning_result.module.prerequisites.length > 0
			) {
				$scope.resourceTabs.push({ name: 'Pre-requisite resources', class: nextClass[nextClassCounter], url: 'prerequisite' });
				tabLocation.prerequisite = nextClassCounter;
				nextClassCounter++;
				$scope.showPrereQuisitePage = true;
			}

			if (
				$scope &&
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.required_modules &&
				$scope.activeResourceDetails.learning_result.module.required_modules.length > 0
			) {
				$scope.resourceTabs.push({ name: 'Required modules', class: nextClass[nextClassCounter], url: 'requiredmodules' });
				$scope.requiredModuleTab = nextClassCounter;
				tabLocation.requiredmodules = nextClassCounter;
				nextClassCounter++;
				$scope.showRequiredModulesPage = true;
			}

			// Lesson resources
			if (
				(
					$scope &&
					$scope.activeResourceDetails &&
					!$scope.activeResourceDetails.instructor_lead &&
					$scope.activeResourceDetails.learning_result &&
					$scope.activeResourceDetails.learning_result.module &&
					$scope.activeResourceDetails.learning_result.module.is_course &&
					$scope.activeResourceDetails.learning_result.approved &&
					$scope.activeResourceDetails.learning_result.module.modules.length > 0
				) &&
				(
					(
						!$scope.hideLaunchButton &&
						$scope.activeResource &&
						$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links.length !== 0 &&
						(
							$scope.activeResource.schedule_index ||
							$scope.activeResource.schedule_index === 0
						) &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.user_link.approved !== 0 &&
			(($scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.cost > '0' &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.user_link.is_paid == '1') ||
			($scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.cost == '0' ||
			$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.cost==null
			)
			)
					) ||
					(
						!$scope.hideLaunchButton &&
						$scope.activeResource &&
						$scope.activeResourceDetails.learning_result.approved !== 0 &&
						$scope?.activeResourceDetails?.learning_result?.module?.schedule_lesson_links &&
						$scope.activeResourceDetails.learning_result.module.schedule_lesson_links.length === 0 &&
			((
			  $scope.activeResourceDetails.learning_result.module.cost > '0' &&
			  $scope.activeResourceDetails.learning_result.is_paid == '1'
			) || ($scope.activeResourceDetails.learning_result.module.cost == '0' || $scope.activeResourceDetails.learning_result.module.cost==null))
					)
				)
			) {
				$scope.resourceTabs.push({ name: '%%lesson_resources%%', class: nextClass[nextClassCounter], url: 'lessoninfo' });
				tabLocation.lessoninfo = nextClassCounter;
				nextClassCounter++;
				$scope.showLessonInfoPage = true;
			}

			if (
				$routeParams.tab &&
				tabLocation[$routeParams.tab]
			) {
				$scope.activeTab = $scope.resourceTabs[tabLocation[$routeParams.tab]];
			} else {
				$scope.activeTab = $scope.resourceTabs[0];
			}

		};

		// Clicking on tbs for specific resource, overview/details/prereqs/sign off/etc.
		$scope.setActiveTab = function (tab, url, ignore_scroll) {
			var i,
				tab_url = false
			;
			if (tab) {
				$scope.activeTab = tab;
				if ($scope.activeTab.url) {
					tab_url = $scope.activeTab.url;
				}
			} else if (url) {
				// find tab by given url
				for (i = $scope.resourceTabs.length - 1; i >= 0; i--) {
					if (
						$scope.resourceTabs[i].url &&
						$scope.resourceTabs[i].url === url
					) {
						tab_url = $scope.resourceTabs[i].url;
						$scope.activeTab = $scope.resourceTabs[i];
					}
				}
			}
			if (tab_url) {
				$location.path('learner/resources/' + $scope.resource_param + '/' + tab_url);
				if (!ignore_scroll) {
					smoothScroll(document.querySelector('body'));
				}
			}
		};


		// All of these filters will also check against "Include completed courses".


		//In progress list, show only those with 'completion_status: "in progress" '
		$scope.recommendationsFilter = function (resource) {

			return resource.is_recommended && !$scope.dismissedRecommendations.includes(resource.learning_module_id);
		};

		//In progress list, show only those with 'completion_status: "in progress" '
		$scope.inProgressFilter = function (resource) {
			return resource.completion_status === 'in progress' &&
				$scope.resources.check(resource) &&
				(
					(
						resource.module.type &&
						resource.module.type.slug !== 'blog_entry'
					) ||
					resource.module.is_course
				)
				;
		};


		// Latest 10 releases
		$scope.latestReleases = function (resource) {
			return $scope.resources.check(resource) &&
				resource.module.type &&
				resource.module.type.slug === 'e_learning' &&
				resource.module.highlight
				;
		};

		// Due soon list, show everything that is not completed, ordered by due
		$scope.dueSoonFilter = function (resource) {
			return resource.completion_status !== 'completed' &&
				resource.completion_status !== ('%%event_completion_state_completed%%').toLowerCase() &&
				$scope.resources.check(resource) &&
				(
					(
						resource.module.type &&
						resource.module.type.slug !== 'blog_entry'
					) ||
					resource.module.is_course
				)
				;
		};

		// Category Filter where return all entries relevant to that category
		$scope.categoryFilter = function (category, forceshowBlogs) {
			forceshowBlogs = forceshowBlogs || false;
			return function (resource) {
				if (
					resource.module.category_id === category.id &&
					(
						resource.module.type &&
						resource.module.type.slug === 'blog_entry'
					)
				) {
					category.hasBlogs = true;
				}

				return resource.module.category_id === category.id &&
					$scope.resources.check(resource) &&
					(
						(
							!category.showBlogs &&
							!forceshowBlogs &&
							(
								(
									resource.module.type &&
									resource.module.type.slug !== 'blog_entry'
								) ||
								resource.module.is_course
							)
						) ||
						(
							category.showBlogs ||
							forceshowBlogs
						)
					)
					;
			};
		};

		$scope.noCategoryFilter = function (resource) {
			var response = false;

			if (
				$scope.resources.check(resource) &&
				resource.module.type &&
				resource.module.type !== 'form-type'
			) {
				if (!resource.module.category_id) {
					response = true;
				}

				if (
					resource.module.category &&
					(
						!resource.module.category.status ||
						resource.module.category.status === 0
					)
				) {
					response = true;
				}
			}

			return response;
		};

		// To show "No Results" filter, that show all resources count
		$scope.allowNoResultsMessage = false;
		$scope.allResourcesFilter = function (resource) {
			var response = false;

			if ($scope.resources.check(resource)) {
				response = true;
			}

			return response;
		};

		// By clicking "browse all", scroll page down to list of all available resources
		$scope.scrollToResources = function () {
			if (document.querySelector('.learner-resource__browse')) {
				smoothScroll(document.querySelector('.learner-resource__browse'));
			}
		};


		// Check mark for each resource, shows different icon/colour depending on completion statuss.
		// TODO: have to display locked icon if prerequisites are not completed.
		$rootScope.statussCheck = function (resource, is_qa) {

			if (resource) {
				var checkClass;

				switch (resource.completion_status) {
					case 'in progress':
						checkClass = 'fa-check orange';
						break;

					case 'completed':
						checkClass = 'fa-check green';
						break;

					case ('%%event_completion_state_completed%%').toLowerCase():
						checkClass = 'fa-check green';
						break;

					case ('%%event_completion_state_completed%%'):
						checkClass = 'fa-check green';
						break;

					default:
						checkClass = 'hidden';
				}

				if (is_qa) {
					switch (resource.qa) {
						case 'Accepted':
							checkClass = 'fa-thumbs-o-up green';
							break;

						case 'Rejected':
							checkClass = 'fa-thumbs-o-down red';
							break;

						default:
							checkClass = 'hidden';
					}
				}

				if (
					resource.module &&
					resource.module.do_prerequisite &&
					!is_qa
				) {
					checkClass = 'fa-lock blue';
				}

				if (resource.passing_status == 'failed') checkClass = 'fa-times red';

				return checkClass;
			}

		};

		$scope.refreshNotificationMaxDate = () => {
			const refreshNotificationTimings = $rootScope.config.RefresherNotificationTimings;

			// Split the string into an array of strings
			// const numbersArray = refreshNotificationTimings.split(',');

			// Convert each string to a number using map
			// const numbers = numbersArray.map(function (numString) {
			// 	return parseInt(numString, 10);
			// });

			// Find the maximum number using Math.max
			const highestNumber = Math.max.apply(null, refreshNotificationTimings);

			// Get today's date
			const today = new Date();

			const futureDate = new Date(today);

			return futureDate.setDate(today.getDate() + highestNumber);
		}

		// Completed, in progress, favorites show functionality.
		// By default all are visible.
		// "not attempted" is allways visible.
		$scope.resources = {
			states: [
				"completed",
				"in progress",
				"failed",
				"mandatory",
				"refresher",
				"locked",
				"available"
			],
			type: {},
			reset: {
				completed: true,
				inprogress: true,
				notstarted: true,
				failed: true,
				locked: true,
				available: true,
				refresher: true,
				mandatory: true,
				favorite: true,
				rejected: true,
				accepted: true,
			},
			show: {
				completed: true,
				inprogress: true,
				notstarted: true,
				failed: true,
				locked: false,
				available: false,
				refresher: false,
				mandatory: false,
				favorite: false,
				rejected: true,
				accepted: true,
			},
			showAll: $cookies.get('resourceShowAll_<?=$mysqlDbName?>') === 'false' ? false : true, // Just for apprentix, to show also resources that are not in standards
			showAllToggle: function (type) {
				if (
					type === 'initial' &&
					!$cookies.get('resourceShowAll_<?=$mysqlDbName?>')
				) {
					$scope.resources.showAll = true;
				} else if (type !== 'initial') {
					$scope.resources.showAll = !$scope.resources.showAll;
					//$scope.resources.showAll = Boolean(type);
				}
				$cookies.put('resourceShowAll_<?=$mysqlDbName?>', $scope.resources.showAll);
			},
			filteredIDs: [], // Apprentix, when issue is selected, its modules will be shown only
			hideSideBar: $cookies.get('hideSideBar_<?=$mysqlDbName?>') === 'false' ? false : true, //false,
			sideBarToggle: function (type, force = false) {
				if (
					type === 'initial' &&
					!$cookies.get('hideSideBar_<?=$mysqlDbName?>')
				) {
					$scope.resources.hideSideBar = force;
				} else if (type === 'initial' && force) {
					$scope.resources.hideSideBar = true;
				}
				else if (type !== 'initial') {
					$scope.resources.hideSideBar = !$scope.resources.hideSideBar;
				}
				$cookies.put('hideSideBar_<?=$mysqlDbName?>', $scope.resources.hideSideBar);
			},

			favorite: function (resource) {
				// Update learning results with favorite state
				$http({
					method: 'GET',
					url: '<?=$LMSUri?>learning/favorite/' + resource.id
				}).then(function successCallback() {
					resource.favorite = !resource.favorite;
				}, function errorCallback() {
					$window.alert('Favorite could not be set, please contact your manager.');
				});

			},
			/*Sorting Filter for Alphabetical and Completion date wise sorting*/
			sortingFilter: function (sortBy) {
				$http({
					method: 'POST',
					url: "<?=$LMSUri?>mylearning/resource-sorting-order",
					data: {
						sort_resource_order: sortBy
					}
				}).then(function successCallback() {
					$rootScope.sortResourceOrder = sortBy;
				});
			},

			toggle: function (state, force = false) {
				if (force) {
					$scope.resources.show[state] = $scope.resources.reset[state];
				} else {
					$scope.resources.show[state] = !$scope.resources.show[state];
				}


				// Saving filter state in cookie
				var filterCookie = "";
				Object.entries($scope.resources.show).forEach(entry => {
					const [key, value] = entry;
					filterCookie = filterCookie + (value ? '1' : '0');
				});
				$cookies.put('filterCookie_<?=$mysqlDbName?>', filterCookie);
			},
			filterConf: function(){
				/*Config option for filters on first visit*/

				if($rootScope.config.isProgrammeFilterVisibleonFirstLogin){
					$scope.resources.hideSideBar = false;
					$cookies.put('hideSideBar_<?=$mysqlDbName?>', $scope.resources.hideSideBar);
				}
				else{
					$scope.resources.hideSideBar = true;
					$cookies.put('hideSideBar_<?=$mysqlDbName?>', $scope.resources.hideSideBar);
				}

				if($rootScope.config.isLearningOutsideProgrammeVisibleonFirstLogin){
					$scope.resources.showAll = true;
					$cookies.put('resourceShowAll_<?=$mysqlDbName?>', $scope.resources.showAll);
				}else{
					$scope.resources.hideSideBar = true;
					$cookies.put('resourceShowAll_<?=$mysqlDbName?>', $scope.resources.showAll);
				}


				$scope.resources.show['completed'] =   $rootScope.config.isCompletedLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['inprogress'] =  $rootScope.config.isInProgressLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['notstarted'] =  $rootScope.config.isNotStartedLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['failed'] =  	   $rootScope.config.isFailedLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['locked'] =      $rootScope.config.isLockedLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['available'] =   $rootScope.config.isEnrollableLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['refresher'] =   $rootScope.config.isRefresherLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['mandatory'] =   $rootScope.config.isMandatoryLearningVisibleonFirstLogin?true:false;
				$scope.resources.show['favorite'] =    $rootScope.config.isFavouriteLearningVisibleonFirstLogin?true:false;

				//condition based filter
				if($rootScope.config.isProgrammeFilterVisible){
					$scope.resources.hideSideBar = false;
					$cookies.put('hideSideBar_<?=$mysqlDbName?>', $scope.resources.hideSideBar);
				}
				else{
					$scope.resources.hideSideBar = true;
					$cookies.put('hideSideBar_<?=$mysqlDbName?>', $scope.resources.hideSideBar);
				}

				if($rootScope.config.isLearningOutsideProgrammeFilterVisible){
					$scope.resources.showAll = true;
					$cookies.put('resourceShowAll_<?=$mysqlDbName?>', $scope.resources.showAll);
				}else{
					$scope.resources.hideSideBar = true;
					$cookies.put('resourceShowAll_<?=$mysqlDbName?>', $scope.resources.showAll);
				}
			},
			set: function (state, value) { // puts show state statein cookie
				$cookies.put(state + 'ResourcesShow_<?=$mysqlDbName?>', value);
			},
			get: function (state) {
				return $cookies.get(state + 'ResourcesShow_<?=$mysqlDbName?>') === 'true';
			},
			categoryCheck:function (category,lists){
				let categoryList = lists.filter(e=>e.module.category_id==category.id);
				let filterResources = categoryList.filter(e => {
					return $scope.resources.check(e) && e.module && (!e.module.type || (e.module.type && ((e.module.type.slug == 'blog_entry' && category.showBlogs) || (e.module.type.slug != 'blog_entry')) && (($rootScope.config.hideMeetingAndZoom && (e.module.type.slug != 'microsoft_teams' && e.module.type.slug != 'zoom_meeting')) || !$rootScope.config.hideMeetingAndZoom)))
				});

				return filterResources.length ? true:false;
			},
			check: function (resource) { // checks if given resource should be shown against all state states
				var response = false,
					i, j
				;

				// Debug logging for ACT Awareness resource
				var isACTResource = resource && resource.module && resource.module.name &&
					resource.module.name.toLowerCase().includes('action counters terrorism');

				if (isACTResource) {
					console.log('🔍 DEBUG: Checking ACT Awareness resource:', resource.module.name);
					console.log('🔍 Resource ID:', resource.module.id);
					console.log('🔍 Initial response:', response);
					console.log('🔍 Filter states:', $scope.resources.show);
				}

				// Completed
				if (
					$scope.resources.show.completed &&
					(
						(
							(
								resource.module.user_programme_forms == undefined ||
								resource.module.user_programme_forms.length == 0
							) &&
							(
								resource.module.user_forms == undefined ||
								resource.module.user_forms.length==0
							) &&
							(
								resource.completion_status === 'completed' ||
								resource.completion_status === ('%%event_completion_state_completed%%').toLowerCase()
							)
						) ||
						$scope.checkUserFormFilter(resource,"Completed")
					)
					&& resource.passing_status !== 'failed'
				) {
					response = true;
					if (isACTResource) {
						console.log('✅ DEBUG: ACT resource matched COMPLETED filter');
						console.log('   - show.completed:', $scope.resources.show.completed);
						console.log('   - completion_status:', resource.completion_status);
						console.log('   - passing_status:', resource.passing_status);
					}
				}

				// In Progress
				if (
					$scope.resources.show.inprogress &&
					(
						(
							resource.completion_status === 'in progress' &&
							(
								resource.module.user_programme_forms == undefined ||
								resource.module.user_programme_forms.length == 0
							) &&
							(
								resource.module.user_forms == undefined ||
								resource.module.user_forms.length == 0
							)
						) ||
						$scope.checkUserFormFilter(resource,"In Progress") ||
						$scope.checkUserFormFilter(resource,"Awaiting Sign-off")
					)
					&& resource.passing_status !== 'failed'
				) {
					response = true;
					if (isACTResource) {
						console.log('✅ DEBUG: ACT resource matched IN PROGRESS filter');
						console.log('   - show.inprogress:', $scope.resources.show.inprogress);
						console.log('   - completion_status:', resource.completion_status);
						console.log('   - passing_status:', resource.passing_status);
					}
				}

				// Not Started
				if (
					$scope.resources.show.notstarted &&
					(
						(
							(
								resource.completion_status === 'not attempted' ||
								resource.completion_status === 'did not attend' ||
								resource.completion_status == '%%event_completion_state_not_attempted%%' ||
								resource.completion_status == ''
							) &&
							(
								resource.module.user_programme_forms == undefined ||
								resource.module.user_programme_forms.length == 0
							) &&
							(
								resource.module.user_forms == undefined ||
								resource.module.user_forms.length == 0
							)
						) ||
						$scope.checkUserFormFilter(resource,"Not Started")
					)
					&& resource.passing_status !== 'failed'
				) {
					response = true;
					if (isACTResource) {
						console.log('✅ DEBUG: ACT resource matched NOT STARTED filter');
						console.log('   - show.notstarted:', $scope.resources.show.notstarted);
						console.log('   - completion_status:', resource.completion_status);
						console.log('   - passing_status:', resource.passing_status);
					}
				}

				if (
					$scope.resources.show.failed
					&& resource.passing_status === 'failed'
				)
				{
					response = true;
				}

				// Available for enrollment
				if (
					$scope.resources.show.available &&
					(
						resource.force_enroll ||
						(
							resource.assigned !== 1 &&
							resource.self_enroll &&
							resource.completion_status !== 'completed' &&
							resource.completion_status !== ('%%event_completion_state_completed%%').toLowerCase() &&
							resource.completion_status !== 'in progress'
						)
					)
				) {
					response = true;
				}
				// Locked
				if (
					$scope.resources.show.locked &&
					(resource.module.do_prerequisite || resource.isLocked)
				) {
					response = true;
				}

				// Refresher
				if (
					$scope.resources.show.refresher &&
					resource.module.refresh
				) {
					response = true;
					if (isACTResource) {
						console.log('✅ DEBUG: ACT resource matched REFRESHER filter');
						console.log('   - show.refresher:', $scope.resources.show.refresher);
						console.log('   - module.refresh:', resource.module.refresh);
					}
				} else if (isACTResource && resource.module.refresh) {
					console.log('❌ DEBUG: ACT resource FAILED refresher filter check');
					console.log('   - show.refresher:', $scope.resources.show.refresher);
					console.log('   - module.refresh:', resource.module.refresh);
					console.log('   - This is likely why the resource is hidden!');
				}

				// Mandatory
				if (
					$rootScope.config.isMandatoryLearningFilterVisible &&
					$scope.resources.show.mandatory &&
					resource.module.category &&
					resource.module.category.is_mandatory === true
				) {
					response = true;
				}

				// Favorite
				if (
					$rootScope.config.isFavouriteLearningFilterVisible &&
					$scope.resources.show.favorite &&
					resource.favorite
				) {
					response = true;
				}

				// And then if other types are clicked out, hide them
				if (
					$scope.resources.type &&
					$scope.resources.type.id
				) {
					if (resource.module.type_id !== $scope.resources.type.id) {
						response = false;
					}
				}

				// If is apprentix, look for standard issue modules filter and see if this module is in list to be displayed.
				if (
					$rootScope.isApprentix &&
					$rootScope.standards.length > 0
				) {
					if (
						!$scope.resources.showAll &&
						(
							!resource.standards_cnt ||
							resource.standards_cnt === 0
						) &&
						$scope.resources.filteredIDs.indexOf(resource.module.id) === -1
					) {
						response = false;
						if (isACTResource) {
							console.log('❌ DEBUG: ACT resource FAILED search string filter');
							console.log('   - searchString:', $scope.resources.searchString);
							console.log('   - resource name:', resource.module.name);
						}
					}

					if (
						$scope.resources.filteredIDs !== 'all' &&
						$scope.lif.standard.selected &&
						$scope.lif.standard.selected.id > 0 &&
						$scope.resources.filteredIDs.indexOf(resource.module.id) === -1 &&
						resource.standards_cnt > 0
					) {
						// dsfds
						response = false;
					}
				}

				if (
					$scope.resources.searchString &&
					(
						resource.module.name.toLowerCase().indexOf($scope.resources.searchString.toLowerCase()) === -1 &&
						(
							!resource.module.description ||
							resource.module.description.toLowerCase().indexOf($scope.resources.searchString.toLowerCase()) === -1
						) &&
						(
							!resource.module.keywords ||
							resource.module.keywords.toLowerCase().indexOf($scope.resources.searchString.toLowerCase()) === -1
						) &&
						(
							!resource.module.category ||
							!resource.module.category.name ||
							resource.module.category.name.toLowerCase().indexOf($scope.resources.searchString.toLowerCase()) === -1
						) &&
						(
							!resource.module.id ||
							resource.module.id != $scope.resources.searchString
						)
					)
				) {
					response = false;
				}

				if (
					$rootScope.config.hideResourcesInLesson &&
					!$scope.resources.searchString &&
					resource.module.is_course === 0 &&
					resource.module.courses &&
					angular.isArray(resource.module.courses)
				) {
					for (j = resource.module.courses.length - 1; j >= 0; j--) {
						if (
							resource.module.courses[j].user_learning_modules_count > 0
						) {
							response = false;
						}
					}
				}

				// Hide HideNotApplicableLearning learning, priority nr 1
				if (
					resource &&
					resource.HideNotApplicableLearning
				) {
					response = false;
				}

				// show lessons created by event only if its completed
				if(response) {
					if (
						resource.created_by_event == 1 &&
						resource.completion_status &&
						resource.completion_status != 'completed' &&
						resource.completion_status != 'Not Enrolled'
					) {
						response = false;
					}

					// show if learning is due to be refreshed in line with the maximum refresher notification timing
					if(resource.open_in_events_only == 1 && resource.refresh_period > 0) {
						// check due date is less than the refresh notification timings
						if(resource.due_at != null) {
							const dueAt = new Date(resource.due_at);
							response = dueAt < $scope.refreshNotificationMaxDate();
						}

					}
				}


				// Show enrolled events in learning view if configuration option ShowEventsineLearningView is set to true
				if (
					response &&
					!$rootScope.config.ShowEventsineLearningView
				) {
					if (resource.schedule_id) {
						const isUserEnrolled = resource.users?.some(
							(user) => user.id === $scope.currentUser.id
						) ||
						resource.module.schedule_lesson_links?.some(
							(link) => link.schedule?.user_link?.link_id === $scope.currentUser.id
						);

						response = isUserEnrolled;
					}
				}

				// Hide past events in learning thumbnail list!
				if (
					$rootScope.config.hidePastEventsInLearningView &&
					resource?.module?.schedule_lesson_links?.length &&
					(
						resource?.schedule_index ||
						resource?.schedule_index === 0
					) &&
					resource?.module?.schedule_lesson_links[resource.schedule_index]?.schedule?.start_date
				) {
					start_date = resource?.module?.schedule_lesson_links[resource.schedule_index]?.schedule?.start_date;
					end_date = resource?.module?.schedule_lesson_links[resource.schedule_index]?.schedule?.end_date;
					compare_date = getJsDate(resource?.module?.schedule_lesson_links[resource.schedule_index]?.schedule?.start_date)
					if (end_date) {
						compare_date = getJsDate(resource?.module?.schedule_lesson_links[resource.schedule_index]?.schedule?.end_date)
					}
					if (compare_date < getJsDate()) {
						response = false;
					}
				}

				// Final debug logging for ACT resource
				if (isACTResource) {
					console.log('🎯 DEBUG: FINAL RESULT for ACT resource:', response);
					console.log('   - Resource will be', response ? 'SHOWN' : 'HIDDEN');
					console.log('   - Current filter states:', $scope.resources.show);
					console.log('   - Search string:', $scope.resources.searchString);
					console.log('-----------------------------------');
				}

				return response;
			},
			// If cookies do not exist, set all cookies!
			init: function () {
				// By default show launch button


				// If user is redirected to this page using search field, filter results according to this string.
				if ($rootScope.temporaryResourceSearch) {
					$scope.resources.searchString = $rootScope.temporaryResourceSearch;
					delete $rootScope.temporaryResourceSearch;
				}

				// Reading filter status cookie and setting filters
				var tmpCookie = $cookies.get('filterCookie_<?=$mysqlDbName?>');
				if (Boolean(tmpCookie)) {
					var cookieString = String(tmpCookie);
					var c = 0;
					Object.entries($scope.resources.show).forEach(entry => {
						const [key, value] = entry;
						$scope.resources.show[key] = (cookieString.substr(c, 1) === '1') ? true : false;
						c++;
					});
				}

				// If QA filter is enabled, add it to states for filtering
				if ($rootScope.config.isLearnerQAFilter) {
					$scope.resources.states.push('Rejected');
					$scope.resources.states.push('Accepted');
				}
			},
			progress: function () {
				var i,
					response
					;

				if (
					$scope.activeResourceDetails &&
					$scope.activeResourceDetails.learning_result &&
					$scope.activeResourceDetails.learning_result.module
				) {


					if($scope.activeResourceDetails.learning_result.module.user_forms!==undefined &&
						$scope.activeResourceDetails.learning_result.module.user_forms.length>0)
					{
						response=$scope.activeResourceDetails.learning_result.module.user_forms[0].user_form_status;
					}else if($scope.activeResourceDetails.learning_result.module.user_programme_forms!==undefined &&
						$scope.activeResourceDetails.learning_result.module.user_programme_forms.length>0)
					{
						response=$scope.activeResourceDetails.learning_result.module.user_programme_forms[0].user_form_status;
					}
					else{
						response = $scope.activeResourceDetails.learning_result.completion_status;
						if ($scope.activeResourceDetails.learning_result.passing_status == 'failed') response = 'failed';

						if (!$scope.activeResourceDetails.learning_result.module.track_progress) {
							response = 'Information Only';
						}

						if($scope.isUserOnWaitingList()) {
							response = 'Waiting List';
						}

						if (
							$scope.activeResourceDetails.learning_result.module.type &&
							(
								$scope.activeResourceDetails.learning_result.module.type.slug === 'blog_entry' ||
								$scope.activeResourceDetails.learning_result.module.type.slug === 'upload' ||
								$scope.activeResourceDetails.learning_result.module.type.slug === 'reflective_log' ||
								$scope.activeResourceDetails.learning_result.module.type.slug === 'form'
							) &&
							response === 'in progress' &&
							$scope.resourceSignOffTrainee
						) {
							response = 'Awaiting Approval';
						}


						for (i = 0; i < $scope.activeResourceDetails.learning_result.module.modules.length; i++) {
							if (!$scope.activeResourceDetails.learning_result.module.modules[i].track_progress) {
								$scope.activeResourceDetails.learning_result.module.modules[i].learning_result.completion_status = 'Information Only';
							}
						}
					}
					if($scope.activeResource.schedule_id)
					{
						response = response != "" ? response : "%%event_completion_state_not_attempted%%";
					}
					return response;
				}
			},
			// Checks if thumbnail have default image
			thumbnailDefault: function (resource) {
				var response = false,
					thumbnail = false
					;

				if (
					resource.module &&
					resource.module.safe_thumbnail
				) {
					thumbnail = resource.module.safe_thumbnail;
				} else if (resource.safe_thumbnail) {
					thumbnail = resource.safe_thumbnail;
				}

				if (
					thumbnail &&
					thumbnail.indexOf('default_type') !== -1
				) {
					response = true;
				}

				return response;
			},
			categoryFilter: {
				select: function (category) {
					var i;
					$scope.lif.categoryFilter.selected = category;
					//$scope.resources.categoryFilter.selected = category;
					// Select first resource in category list!

					if (
						category &&
						$scope.resourceListSorted &&
						$scope.resourceListSorted.length > 0
					) {
						$scope.resources.filteredIDs = 'all';
						$rootScope.$broadcast('apprenticeshipstandards-show-all-resources');
						for (i = 1; i <= $scope.resourceListSorted.length; i++) {
							if (
								$scope.resourceListSorted[i] &&
								$scope.resourceListSorted[i].module &&
								$scope.resourceListSorted[i].module.category_id === category.id &&
								$scope.resources.check($scope.resourceListSorted[i])
							) {
								$scope.setActiveResource($scope.resourceListSorted[i], category.name);
								break;
							}
						}
					}
				},
				selected: false
			},
			showCompletionState: function() {
				var response = true;
				/* reverting due to https://openelms.atlassian.net/browse/SCOR-4669 this might need to be revisited in future
				if (
					$scope.activeResource &&
					$scope.activeResource.completion_status &&
					(
						$scope.activeResource.module &&
						(
							!$scope.activeResource.module.disable_upon_completion ||
							(
								$scope.activeResource.module.disable_upon_completion &&
								$scope.activeResource.completion_status !== 'completed'
							)
						)
					)
				) {
					response = true;
				}
				*/
				return response;
			},
			statusIcon: function (resource) {
				var response = false;
				if (resource) {
					if (resource.passing_status === 'failed')
					{
						response = 'icon-failed-fill';
					}
					else if (resource.completion_status === 'not attempted') {
						response = 'icon-notstarted-fill';
					}
					else if (resource.completion_status === 'in progress') {
						response = 'icon-inprogress-fill';
					}
					else if (
						resource.completion_status === 'completed' ||
						resource.completion_status === ('%%event_completion_state_completed%%').toLowerCase()
					) {
						response = 'icon-completed-fill';
						/* reverting due to https://openelms.atlassian.net/browse/SCOR-4669 this might need to be revisited in future
						if (
							resource.module &&
							resource.module.disable_upon_completion
						) {
							response = false;
						}
						*/
					}
				}

				return response;
			},
			// Coppies current browser url in memory prepending SSO url.
			copySsoUrl: function (isSSO= true, catId=null) {
				var saml_url = false;
				if (
					$rootScope.config &&
					$rootScope.config.LMSUrl &&
					$location.url()
				) {
					var urlPart = 'app' + $location.url();
					if (catId) urlPart = 'app/learner/resources/cat-' + catId;

					if (isSSO){
						saml_url = $rootScope.config.LMSUrl + 'saml/?ReturnTo=' + $rootScope.config.LMSUrl + urlPart;
					}else{
						saml_url = $rootScope.config.LMSUrl + urlPart;
					}
				}
				navigator.clipboard.writeText(saml_url);
				$ngConfirm({
					title: 'Address copied!',
					content: 'Address <div>"' + saml_url + '"</div> copied in clipboard.',
					columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
					buttons: {
						ok: {
							text: 'ok',
							btnClass: 'btn-blue',

						},
					}
				});
			},
			thumbnailIcon: function (icon, resource) {
				var response = false;
				if (!resource) {
					return false;
				}
				switch (icon) {
					case 'enroll':

						response =
							resource.force_enroll ||
							(
								resource.assigned !== 1 &&
								resource.self_enroll &&
								resource.completion_status !== 'completed' &&
								resource.completion_status !== ('%%event_completion_state_completed%%').toLowerCase() &&
								resource.completion_status !== 'in progress'
							)

						;

					break;
				}

				return response;
			}
		};
		$scope.resources.init();
		$scope.resources.filterConf();

		$scope.isUserOnWaitingList = function() {
			const activeResourceDetails = $scope.activeResourceDetails;
			const activeUserId = $rootScope.currentUser.id;

			if (activeResourceDetails.schedule_lesson_links && activeResourceDetails.schedule_lesson_links.length > 0) {
				const schedule = activeResourceDetails.schedule_lesson_links[0].schedule;

				if (schedule.waiting && schedule.waiting.length > 0) {
					return schedule.waiting.some(function (item) {
						return item.id === activeUserId;
					});
				}

				if (schedule.waiting_for_approval && schedule.waiting_for_approval.length > 0) {
					return schedule.waiting_for_approval.some(function (item) {
						return item.id === activeUserId;
					});
				}
			}

			return false;
		}

		$scope.checkUserFormFilter= function(resource, status) {
			if (
				(
					resource.module &&
					resource.module.user_programme_forms != undefined &&
					resource.module.user_programme_forms.length > 0 &&
					resource.module.user_programme_forms[0].user_form_status == status
				) ||
				(
					resource.module &&
					resource.module.user_forms != undefined &&
					resource.module.user_forms.length >0 &&
					resource.module.user_forms[0].user_form_status == status
				)
			) {
				return true;
			} else {
				return false;
			}

		}

		// show given resource or "This screen highlights the learning resource which is most urgent (i.e. not Completed and the due date is the oldest.) "
		$scope.displayInitialResource = function () {
			if (
				$rootScope.config.isBrowsebyDefault &&
				!$routeParams.resource
			) {
				$scope.hideResource = true;
				$timeout(function () {
					$scope.activeResource = true;
				}, 300);
				return;
			}

			var tempResource = false,
				tempNonCompleted = false,
				i,
				learner_last_visited_resouce = false,
				j
				;

			// If redirectBackToLesson is set to true, and resource is part of lastVisitedLesson, redirect to lastVisitedLesson
			if (
				$rootScope.config.redirectBackToLesson &&
				$scope.lastVisitedLesson &&
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.is_course === 0
			) {
				for (j = $scope.activeResourceDetails.learning_result.module.courses.length - 1; j >= 0; j--) {
					if (
						$scope.activeResourceDetails.learning_result.module.courses[j].id === $scope.lastVisitedLesson
					) {
						$location.path('learner/resources/' + $scope.lastVisitedLesson + '/lessoninfo');
						return;
					}
				}
			}


			if (!$scope.isCatRoute())
			{
			// if site is loaded with specific resource in URL, use that as default, or in case all module data needs to be reloaded, use active resource module.
			if (
				$routeParams.resource
			) {
				$scope.route_resource_id = $routeParams.resource.toString().split('-')[0];
				$scope.setActiveResource($scope.route_resource_id, false, ($routeParams.resource.toString().split('-')[1] || false));
				$scope.missingResourceCheck();
			} else {
				for (i = 0; i <= $scope.resourceListSorted.length; i++) {
					if ($scope.resourceListSorted[i]) {
						// Put first resource in variable
						if (!tempResource) {
							tempResource = $scope.resourceListSorted[i];
						}

						if (!$scope.activeResource) {
							// Put first non completed resource in variable
							if (
								!tempNonCompleted &&
								$scope.resourceListSorted[i].completion_status !== 'completed'
							) {
								tempNonCompleted = $scope.resourceListSorted[i];
							}

							// If page is loaded with pre-selected category, set first resource in that category as active
							if (
								$scope.lif.categoryFilter.selected &&
								$scope.resourceListSorted[i].module &&
								$scope.resourceListSorted[i].module.category_id === $scope.lif.categoryFilter.selected.id &&
								(
									$scope.resources.check($scope.resourceListSorted[i]) ||
									$scope.resourceListSorted[i].module.is_course
								)
							) {
								$scope.setActiveResource($scope.resourceListSorted[i], $scope.lif.categoryFilter.selected.name);
								break;
							}
						}

						// Check if last visited resource is in list, if so, use that to display initial resource
						if (parseInt($window.localStorage.getItem('learner_last_visited_resouce'), 10) === $scope.resourceListSorted[i].module.id) {
							learner_last_visited_resouce = $scope.resourceListSorted[i];
						}

					}
				}

				if (
					!$scope.activeResource &&
					learner_last_visited_resouce
				) {
					$scope.setActiveResource(learner_last_visited_resouce);
				}

				// If there was no selected category set first resource as selected!
				if (
					!$scope.activeResource &&
					tempNonCompleted
				) {
					$scope.setActiveResource(tempNonCompleted);
				}

				// in case all resources are completed, but you have not checked show completed.
				if (
					!$scope.activeResource &&
					tempResource
				) {
					$scope.resources.set('completed', 'true');
					$scope.setActiveResource(tempResource);
				}
				$scope.missingResourceCheck();
			}
			}
		};

		$scope.getLinkedModules = function() {
			return $scope.activeResourceDetails.linked || $scope.activeResourceDetails.learning_result.module.linked || [];
		};

		// update Off the job check for result
		$scope.updateOffTheJobTraining = function () {
			if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result
			) {
				$http({
					method: 'PUT',
					url: '<?=$LMSUri?>learning/off_the_job_training/' + $scope.activeResourceDetails.learning_result.id,
					data: {
						off_the_job_training: !$scope.activeResourceDetails.learning_result.off_the_job_training
					}
				}).then(function successCallback() {
					$scope.activeResourceDetails.learning_result.off_the_job_training = !$scope.activeResourceDetails.learning_result.off_the_job_training;
				}, function errorCallback() {
					$window.alert("Update failed!");
				});
			}


		};


		//get due date from shedule or provide expected_completion_date
		$scope.getDueDate = function () {
			var pattern = 'HH:mm ' + $rootScope.config.defaultDateFormat.toUpperCase();
			if (!$scope.activeResourceDetails) {
				return false;
			}
			if ($scope.getResourceProperty('all_day_event')) {
				pattern = $rootScope.config.defaultDateFormat.toUpperCase();
			}
			if ($scope.getResourceProperty('start_date')) {
				if(moment($scope.getResourceProperty('start_date')).isValid()) {
					return moment($scope.getResourceProperty('start_date')).format(pattern);
				}
			}
			if(!moment($scope.getResourceProperty('expected_completion_date')).isValid()){
				return  false;
			}
			return moment($scope.getResourceProperty('expected_completion_date')).format(pattern);
		};

		$scope.getEndDate = function () {
			var pattern = 'HH:mm ' + $rootScope.config.defaultDateFormat.toUpperCase();
			if (!$scope.activeResourceDetails) {
				return false;
			}
			if ($scope.getResourceProperty('all_day_event')) {
				pattern = $rootScope.config.defaultDateFormat.toUpperCase();
			}
			if ($scope.getResourceProperty('end_date')) {
				return moment($scope.getResourceProperty('end_date')).format(pattern);
			}
		};

		// Returns value from activeResourceDetails module object property
		$scope.getResourceProperty = function (property) {
			var response = false;
			//  this long list of checks only to see if this lesson is in schedule/event list with different name, confusing, yes.
			if (
				(
					$scope.activeResource.schedule_index ||
					$scope.activeResource.schedule_index === 0
				) &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links.length > 0 &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index] &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule[property]
			) {
				response = $scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule[property];
			} else if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module[property]
			) {
				response = $scope.activeResourceDetails.learning_result.module[property];
			} else if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails[property]
			) {
				response = $scope.activeResourceDetails[property];
			} else if (
				$scope.activeResource &&
				$scope.activeResource.module &&
				$scope.activeResource.module[property]
			) {
				response = $scope.activeResource.module[property];
			} else if (
				$scope.activeResource &&
				$scope.activeResource[property]
			) {
				response = $scope.activeResource[property];
			}
			return response;
		};


		//------------------------------------

		// return Resource name as safe string back to class name
		$scope.safeResourceName = function (name) {
			return 'learner-resource--' + $textOperations.safeString(name, 'lowercase');
		};


		// Self sign off for specific types of resources: classroom / on the job training / website / book-cd-dvd
		$scope.resourceSignOffConfirm = function () {
			var modalOptions = {
				closeButtonText: 'Cancel',
				actionButtonText: 'Sign Off',
				headerText: '%%sign_off_button%%  "' + $scope.activeResourceDetails.learning_result.module.name + '" ?',
				bodyText: $rootScope.config.signOffText || 'I agree that the information provided here is an accurate account of what has taken place.',
				// if mandatory duration is set true and duration is 0, set this to collect duration in confirmation modal window.
				mandatoryDuration: $rootScope.config.mandatoryDuration &&
					$scope.activeResourceDetails.learning_result.duration_hours === 0 &&
					$scope.activeResourceDetails.learning_result.duration_minutes === 0 &&
					$scope.activeResourceDetails.learning_result.module.duration_hours === 0 &&
					$scope.activeResourceDetails.learning_result.module.duration_minutes === 0,
				duration_hours: 0,
				duration_minutes: 0
			};

			if (!$scope.resourceSignOffTrainee) {
				modalService.showModal({}, modalOptions).then(function (response) {
					// fire request to update "learning_results" table, "sign_off_trainee" field with true
					var sign_off_alert = '%%learner_sign_off__approval%%';
					if ($scope.activeResourceDetails.learning_result.module.require_management_signoff) {
						sign_off_alert = '%%learner_sign_off__confirmed%%';
					}
					$http.put(
						"<?=$LMSUri?>learner/sign-off",
						{
							'id': $scope.activeResource.id,
							response: response
						}
					).then(
						function () {
							$scope.resourceSignOffTrainee = true;
							$scope.resourceSignOffAlerts = [{ type: 'success', msg: sign_off_alert }];
							$rootScope.$broadcast('update-actions');
							$scope.openComments();
							if ($scope.activeResourceDetails?.learning_result?.module?.require_management_signoff) {
								$learnerOperations.getResourceList(true);
							} else {
								$scope.setActiveResourceDetails();
							}
						},
						function () {
							$scope.resourceSignOffAlerts = [{ type: 'danger', msg: sign_off_alert }];
							if ($scope.activeResourceDetails?.learning_result?.module?.require_management_signoff) {
								$learnerOperations.getResourceList(true);
							}
						}
					);
				});
			}
		};
		$scope.closeResourceSignOffAlert = function () {
			$scope.resourceSignOffAlerts = [];
		};

		// Leave Feedback form popup and listener to broadcast
		$scope.fs = $feedbackService;
		$scope.$on('feedback-submitted', function (events, args) {
			$scope.feedbackEvents = events;
			$scope.feedbackAlerts = args;
		});
		$scope.rating = {
			max: new Array(5)
		};

		// Check if resource in lesson can be opened as link
		$scope.lessonResourceIsLink = function (resource) {
			var response = false;
			if (
				resource &&
				resource.material &&
				resource.material.link &&
				(
					resource.type.slug !== 'youtube' &&
					resource.type.slug !== 'vimeo' &&
					resource.type.slug !== 'h5p'
				)
			) {
				response = true;
			}
			return response;
		};


		// I extend this scope with these controllers because I want to re-use  scope parameters.
		// SERVICES IS DA WAY!

		// learner resource, type evidence, set-up meeting
		angular.extend(this, $controller('LearnerResourcesMeetingsEvidence', { $scope: $scope }));

		$scope.closeAlert = function () {
			$scope.alerts = [];
		};
		$scope.closeFeedbackAlert = function () {
			$scope.feedbackAlerts = [];
		};

		// setting classes for specific dynamic elements
		$scope.safeCategoryClass = function (categoryName) {
			return 'learner-resource__category--' + $textOperations.safeString(categoryName, 'lowercase');
		};

		$scope.safeStatusClass = function (status) {
			return 'learner-resource__status--' + $textOperations.safeString(status, 'lowercase');
		};

		$scope.canRetake = function () {
			var compare_date = moment($scope.activeResource.completed_at);
			if (
				$scope.activeResource.completion_status != 'completed' &&
				$scope.activeResource.passing_status == 'failed'
			) {
				if ($scope.activeResource.failed_at) {
					var compare_date = moment($scope.activeResource.failed_at);
				} else {
					var compare_date = moment($scope.activeResource.updated_at);
				}
			}
			var now = moment();
			var diffDays = now.diff(compare_date, 'days');

			return $scope.getResourceProperty('retake_fee')
				&& $rootScope.config.PaymentsEngine
				&& $scope.activeResource?.module?.disable_upon_completion
				&& (
					(
						$scope.resources.progress() === 'completed'
					) ||
					(
						$scope.resources.progress() === 'failed' &&
						$scope.activeResourceDetails?.learning_result?.attempts_left < 1
					)
				)
			 	&& (
					!$rootScope.config.RetakeFeeLimit ||
					diffDays <= $rootScope.config.RetakeFeeLimit
				)
			;
		};

		$scope.retake = function (){
			// if ($scope.activeResource.assigned === 0) {
				$scope.enrollCurrentResource();
			// }
		}

		$scope.safeTypeClass = function (status) {
			return 'learner-resource__type--' + $textOperations.safeString(status, 'lowercase');
		};
		// EOF setting classes for specific dynamic elements


		$scope.loadComments = function () {
			$http({
				method: 'GET',
				url: '<?=$LMSUri?>learning/results/' + $scope.activeResource.id + '/commentslist'
			}).then(function successCallback(data) {
				$scope.learningResultsComments = data.data;
			});
		};

		$scope.openComments = function () {
			var fs = $fileService;
			fs.reset();
			var modalInstance = $uibModal.open({
				animation: true,
				ariaLabelledBy: 'modal-title',
				ariaDescribedBy: 'modal-body',
				templateUrl: '<?=$LMSTplsUriHTML?>modal-learning-results-comment.html?v=<?=$version?>',
				controller: 'ModalInstanceCtrl', // modal window controller
				controllerAs: '$ctrl',
				size: 'lg',
				backdrop: 'static',
				resolve: {
					data: function () {
						return {
							fs: fs
						};
					}
				}
			});

			// fired when modal window is closed, for images, might move somewhere else.
			modalInstance.result.then(function (data) {
				// submit comment to database
				$http({
					method: 'POST',
					url: '<?=$LMSUri?>learning/addlearningresultscomment',
					data: {
						comment: data.comment,
						learning_module_id: $scope.activeResourceDetails.learning_result.module.id,
						learning_results_id: $scope.activeResourceDetails.learning_result.id
					}
				}).then(function successCallback(response) {
					//reload learning results.
					$rootScope.$broadcast('learning-result-comment-added', {id: response.data, fs:fs});
					$rootScope.$broadcast('update-actions');
				});

			}, function () {
				$log.info('Open comments overlay for learning results dismissed at: ' + new Date());
			});
		};

		// Something to do with duration for individual learning result
		$scope.duration = {
			hours: 0,
			minutes: 0,
			showForm: false,
			update: function () {
				$scope.duration.showForm = true;
				$scope.duration.hours = $scope.activeResourceDetails.learning_result.duration_hours;
				$scope.duration.minutes = $scope.activeResourceDetails.learning_result.duration_minutes;
			},
			submit: function () {
				$scope.duration.showForm = false;
				$http({
					method: 'PUT',
					url: '<?=$LMSUri?>learning/update-duration/' + $scope.activeResource.id,
					data: {
						duration_hours: $scope.duration.hours,
						duration_minutes: $scope.duration.minutes
					}
				}).then(function successCallback() {
					$scope.activeResourceDetails.learning_result.duration_hours = $scope.duration.hours;
					$scope.activeResourceDetails.learning_result.duration_minutes = $scope.duration.minutes;
					$rootScope.$broadcast('update-actions');
				});
			}
		};
		$scope.urlPrefix = $textOperations.urlPrefix;

		$scope.$on('apprenticeshipstandards-filter-resources', function (event, args) {
			$scope.apprenticeshipFilterEvent = event;
			$scope.resources.filteredIDs = args;
		});

		$scope.$on('resources-search-type', function (event, args) {
			$scope.resourcesSearchType = event;
			$scope.resources.type = args;
		});

		$scope.$on('resources-search-string', function (event, args) {
			$scope.resourcesSearchStringEvent = event;
			$scope.resources.searchString = args;
			//reset checkbox filter
			$scope.resources.toggle('completed', true);
			$scope.resources.toggle('inprogress', true);
			$scope.resources.toggle('notstarted', true);
			$scope.resources.toggle('locked', true);
			$scope.resources.toggle('available', true);
			$scope.resources.toggle('refresher', true);
			$scope.resources.toggle('mandatory', true);
			$scope.resources.toggle('favorite', true);
			$scope.resources.showAllToggle("initial");
			//End here
			//categrory filter
			$scope.resources.categoryFilter.select(false);
			//catagory filter end here
			//Program filter

			$rootScope.$broadcast("reset-search-program");
			// $cookies.remove('hideSideBar_<?=$mysqlDbName?>')
			// $scope.resources.sideBarToggle('initial',true)

			//End program filter
			//Sorting filter
			// Not sure why this is here
			if ($rootScope.sortResourceOrder !== 'module.name') {
				$scope.resources.sortingFilter('module.name');
				//sorting filter end
			}
		});

		$scope.$on('launch-resource', function (event, args) {
			$scope.launchResourceEvent = event;
			$scope.launchLessonResource(args);
		});

		$scope.$on('set-active-resource', function (event, args) {
			$scope.setActiveResourceEvent = event;
			$scope.setActiveResource(args);
		});

		$scope.$on('learner-resources-reload-route', function () {
			$route.reload();
		});


		// If anders pink is enabled, retrieve data
		if ($rootScope.config.andersPinkEnabled) {
			$scope.andersPink = {
				briefings: [],
				showBriefing: function (briefing) {
					$uibModal.open({
						animation: true,
						ariaLabelledBy: 'modal-title',
						ariaDescribedBy: 'modal-body',
						templateUrl: '<?=$LMSTplsUriHTML?>modal-view-pink-anders-briefing.html?v=<?=$version?>',
						controller: 'modalViewPinkAndersBriefing',
						size: 'lg',
						backdrop: 'static',
						resolve: {
							data: function () {
								return {
									briefing: briefing
								};
							}
						}
					});
				}
			};
			$http({
				method: 'GET',
				url: '<?=$LMSUri?>anders-pink/briefings'
			}).then(function successCallback(response) {
				if (
					response.data &&
					response.data.data &&
					response.data.data.owned_briefings
				) {
					$scope.andersPink.briefings = response.data.data.owned_briefings;
				}
			});
		}

	$scope.checkVideoPromise = null;

	$scope.checkVideo = function (resource_id, resource_type) {

		$scope.checkVideoPromise = null;

		// Open popup based on resource type
		if (resource_type === "zoom_meeting") {
			$scope.videoPopup = $window.open(
				"<?=$LMSUri?>zoom/video/" + resource_id,
				"_blank",
				"width=600,height=400"
			);
		} else if (resource_type === "microsoft_teams") {
			$scope.videoPopup = $window.open(
				"<?=$LMSUri?>teams/onedrive/" + resource_id,
				"_blank",
				"width=600,height=400"
			);
		}

		// Start checking if popup is closed after short delay
		$timeout(function () {
			$scope.checkVideoPromise = $interval(function () {
				let isClosed = false;

				try {
					// 'closed' is safe to access cross-origin
					isClosed = !$scope.videoPopup || $scope.videoPopup.closed;
				} catch (e) {
					// In case of unexpected errors (very rare)
					isClosed = true;
				}

				if (isClosed) {
					$interval.cancel($scope.checkVideoPromise);
					$learnerOperations.getResourceList(true);
				}

				// Removed unsafe 'name' access
			}, 500);
		}, 500);

	};

		// set initial resource if someone navigated to "/learner/resources" within confinments of this controller
		$scope.$on('$locationChangeSuccess', function () {
			var current_route = $location.url();
			if (current_route === '/learner/resources') {
				$rootScope.$broadcast('learner-resources-reload-route');
			}
		});

		$scope.checkInWaitingList = function() {
			if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links &&
				$scope.activeResource &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index] &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.waiting &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.waiting[0] &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.waiting[0].is_paid == '0' &&
				$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.waiting[0].approved == '1'
			) {
				return true;
			} else {
				return false;
			}
		};

		$scope.getEventDate = function (resource) {
			var response = false;
			if (
				resource.module.schedule_lesson_links &&
				resource.module.schedule_lesson_links[resource.schedule_index] &&
				resource.module.schedule_lesson_links[resource.schedule_index].schedule &&
				resource.module.schedule_lesson_links[resource.schedule_index].schedule.event_date_range
			) {
				response = resource.module.schedule_lesson_links[resource.schedule_index].schedule.event_date_range;
			}
			return response;
		};

		// returns date for currently selected lessons resource,
		$scope.getLessonResoucesDate = function(resource) {
			var learning_result = resource.learning_result,
				response = false
			;
			/*
			This is priority level of dates:
				completion_date_custom
				Event-lesson date
				Lesson date - taken from activeresourcedetails
				Grace at
				Due at
			*/

			// Lowest priority first - learning result dates
			if (learning_result) {
				if (learning_result.due_at) {
					response = learning_result.due_at;
				}
				if (learning_result.grace_at) {
					response = learning_result.grace_at;
				}
			}

			// Next is lesson!
			if (
				$scope.activeResourceDetails &&
				$scope.activeResourceDetails.learning_result &&
				$scope.activeResourceDetails.learning_result.module &&
				$scope.activeResourceDetails.learning_result.module.is_course
			) {
				if ($scope.activeResourceDetails.learning_result.due_at) {
					response = $scope.activeResourceDetails.learning_result.due_at;
				}
				if ($scope.activeResourceDetails.learning_result.grace_at) {
					response = $scope.activeResourceDetails.learning_result.grace_at;
				}

				// Event dates!
				if (
					$scope.activeResource &&
					$scope.activeResource.schedule_index &&
					$scope.activeResourceDetails.learning_result.module &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index] &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.start_date
				) {
					response = $scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index].schedule.start_date;
				}

				// Lessons completion_date_custom trumps event date
				if ($scope.activeResourceDetails.learning_result.completion_date_custom) {
					response = $scope.activeResourceDetails.learning_result.completion_date_custom;
				}

			}

			// Learning result completion_date_custom trumps it all!
			if (learning_result) {
				if (learning_result.completion_date_custom) {
					response = learning_result.completion_date_custom;
				}
			}

			return response;

		};

		$scope.submitCancellationReason = false;

		$scope.events = {
			showCancel: function (link) {
				var response = false;
				if (
					!link &&
					$scope.activeResource &&
					$scope.activeResourceDetails &&
					$scope.activeResourceDetails.learning_result &&
					$scope.activeResourceDetails.learning_result.module &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links &&
					$scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index]
				) {
					link = $scope.activeResourceDetails.learning_result.module.schedule_lesson_links[$scope.activeResource.schedule_index];
				}
				if (
					link?.schedule &&
                    moment(link.schedule.end_date).isAfter(moment()) &&
					(
						(
							link.schedule.waiting?.length > 0 ||
							link.schedule.waiting_for_approval?.length > 0
						) ||
						(
							link.schedule.enrole_any_learner &&
							link.schedule.enrole_any_learner === 1
						)
					) &&
					(
						link.schedule.user_link ||
						link.schedule.waiting?.length > 0 ||
						link.schedule.waiting_for_approval?.length > 0
					)
				) {
					response = true;
				}

				return response;
			},
			cancel: function (link) {
				$ngConfirm({
					title: 'Cancelling an Event!',
					columnClass: 'col-md-6 col-md-offset-3',
					content: '<h2>{{title}}</h2>' +
					'<div class="alert alert-danger" ng-if="!cancellation_reason && submitCancellationReason">Please enter a valid reason for cancelling.</div>' +
					'<div style="padding-bottom: 3px;">' +
					'<div>Do you want to cancel this learning session, you can select another on the calendar if available</div>' +
					'<div class="form-group"><label>Please enter the reason for cancelling:</label>'+
					'<textarea ng-model="cancellation_reason" class="form-control"></textarea></div>' +
					'</div>',
					//content: 'Do you want to cancel this learning session, you can select another on the calendar if available',
					buttons: {
						confirmCancellation: {
							text: 'Cancel event',
							btnClass: 'btn-blue',
							action: function(scope, button) {

								scope.submitCancellationReason = true;

								if (!scope.cancellation_reason) {
									return false;
								}

								$http({
									method: 'POST',
									url: '<?=$LMSUri?>schedule/link/delete',
									data: {
										link_id: $rootScope.currentUser.id,
										schedule_id: link.schedule.id,
										type: "users",
										cancellation_reason: scope.cancellation_reason,
										cancellation_type: 'cancelled_by_user'
									}

								}).then(function successCallback (response){
									if(response.data.refund_status == 1){
										$ngConfirm({
											title: "Refund Initiated",
											content: "The refund process is not automatically initiated - any transfer of payment to a new course they need to email CPD support team.",//Message added
											columnClass: 'col-md-6 col-md-offset-3',
											type: 'green',
											typeAnimated: true,
											buttons: {
												tryAgain: {
													text: 'Okay',
													btnClass: 'btn-green',
													action: function () {
														$location.path('learner/resources');
													}
												},
											}
										});
									}else {
										$location.path('learner/resources');
									}
									$learnerOperations.getResourceList(true);

								}).catch((err) => {
									if(err.data.error) {
										$ngConfirm({
											title: "Alert",
											type: "red",
											content: err.data.error,
											typeAnimated: true,
											columnClass: 'col-md-6 col-md-offset-3',
											buttons: {
												tryAgain: {
													text: 'ok',
													btnClass: 'btn-danger',
													action: function() {

													}
												},
											}
										});
									}
								});
							}
						},
						exit: {
							text: 'Abort',
							btnClass: 'btn-red',
							action: function(scope, button){
								scope.submitCancellationReason = false;
							}
						},
					}
				});
			},
			labelColor: function (resource) {
				if (
					resource.module.schedule_lesson_links &&
					resource.module.schedule_lesson_links[resource.schedule_index] &&
					resource.module.schedule_lesson_links[resource.schedule_index].schedule &&
					resource.module.schedule_lesson_links[resource.schedule_index].schedule.user_link
				){
					let user = resource.module.schedule_lesson_links[resource.schedule_index].schedule.user_link;
					if ( user.type === "users") {
						if (user.approved) {
							return "event-item-header--enrolled";
						} else {
							return "event-item-header--waiting";
						}
					} else {
						return "event-item-header--waiting";
					}
				} else {
					return "event-item-header--enrollable";
				}
			}
		};

		$scope.trustHtml = function (comment) {
			return $sce.trustAsHtml(comment);
		};

		// Load first 1000 resources associated with this learner, call for it is in service, and it broadcasts when ready.
		$scope.$on('resource-list-retrieved', function (event, args) {
			$scope.resourceList = args;
			$scope.resourceListSorted = [];

			$scope.resourceListEvent = event;
			$scope.resourceListSorted = $filter('orderBy')($scope.resourceList, 'due_at');
			$scope.resourceListSorted.forEach(r =>    // sorting date -> for events we take event start date, for other resources - due date.
			{
				var dateSort;
				if (r?.start_date) {
					dateSort = Date.parse(r.start_date);
				}
				else if (r.module?.schedule_lesson_links?.length > 0 && r.schedule_id)
				{
					r.module.schedule_lesson_links.forEach( shc =>
					{
						if (shc.schedule_id == r.schedule_id) dateSort = Date.parse(shc.schedule.start_date);
					});
				}
				else dateSort = Date.parse(r?.date_due?.date);
				r.date_sort = dateSort;
			});
			$scope.displayInitialResource();

			$timeout(function () {
				$scope.allowNoResultsMessage = true;
			}, 2500);
		});

		// This will listen for route parameter changes and will load resource/tabs as needed from route changes, no need to use on clicks everywhere.
		$scope.$on("$locationChangeStart", function (event, next, current) {
			$timeout(function () {
				var new_resource_id,
					schedule_id
					;
				// Process data only if resource is set in URL
				if ($scope.isCatRoute())
				{
					$scope.selectCategoryFilterById($routeParams?.resource?.split('-')[1]);
				}
				else if (
					$routeParams.resource
					//&& $scope.activeResource
				) {
					// Get resource ID, as it can be composed from resource_id-schedule_id
					new_resource_id = parseInt($routeParams.resource.toString().split('-')[0], 10);
					// Get schedule ID, if exists
					schedule_id = $routeParams.resource.toString().split('-')[1] ? parseInt($routeParams.resource.toString().split('-')[1], 10) : false;
					// If user navigates and new resource id is not the same or schedule ID is not the same, set active resource again
					if (
						!$scope.activeResource ||
						$scope.activeResource.learning_module_id !== new_resource_id ||
						(
							schedule_id &&
							$scope.activeResourceDetails.schedule_id !== schedule_id
						) ||
						(
							$scope.old_schedule_id &&
							!schedule_id
						)
					) {
						$scope.old_resource_id = new_resource_id;
						$scope.old_schedule_id = schedule_id;
						$scope.setActiveResource(new_resource_id, false, schedule_id);
						$scope.missingResourceCheck();
						// If active tabs are manipulated, set them!
					} else if (
						$scope.activeTab &&
						(
							$routeParams.tab ||
							$scope.activeTab.url
						) &&
						$scope.activeTab.url !== $routeParams.tab
					) {
						if ($routeParams.tab) {
							$scope.setActiveTab(false, $routeParams.tab);
						} else {
							$scope.setActiveTab($scope.resourceTabs[0]);
						}
					}
				}
			}, 100);
		});

		$learnerOperations.getResourceList(false);

		$scope.$on('learning-result-comment-added', function (event, data) {
			$scope.learningResultCommentAddedEvent = event;

			if (data.fs) {
				data.fs.conf = {
					table_row_id: data.id,
					table_name: 'learning_results_comments'
				};
				data.fs.upload();
			}
			if (data.fs.upload_files.length === 0) {
				$scope.loadComments();
			}
		});

		$scope.$on('files-refresh', function (event, data) {
			$scope.filesRefreshEvent = event;
			if (data > 0) {
				$scope.loadComments();
			}
		});

		$scope.handleResourceClick = function(event, resource)
		{
			$timeout(() => document.getElementsByClassName('btn-launch')?.[0]?.focus(), 500);
			if (
				resource.module.type &&
				resource.module.type === "form-type"
			) {
				event.preventDefault();
				//$scope.showLearnerProgress();
				$scope.ss.viewForm(resource, resource.user_id,'assigned_form')
			}

			if (
				resource?.module?.is_course &&
				!resource?.schedule_id
			) {
				$scope.checkLessonEnroll(resource.module.id, event);
			}
		}

		// check if clicked lesson that has no events attached to has events that can be enrolled to.
		$scope.checkLessonEnroll = function (resource, event) {
			var modalInstance;

			var returnHere = true;

			let resourcesListPass = $scope.resourceListSorted.filter((row) => row.module.id == resource && row.schedule_id);
			const lessonDetails = $scope.resourceListSorted.filter((row) => row.module.id == resource && !row.schedule_id);
			const lessonDetail = lessonDetails[0];

			// checking every event in the lesson is 'Not Enrolled'
			let everyEventNotEnrolled = resourcesListPass.every((resource) => resource.completion_status == 'Not Enrolled');

			const notEnrolledEventNotExists = resourcesListPass.every((resource) => resource.completion_status != 'Not Enrolled');
			let showNoEventsAlert = resourcesListPass.length == 0 && lessonDetail && lessonDetail.module && lessonDetail.module.refresh;

			if (!everyEventNotEnrolled) {
				// check which event is not not-enrolled if refresh lesson then show coming events
				everyEventNotEnrolled = resourcesListPass.filter((resource) => resource.completion_status != 'Not Enrolled').every((resource) => resource.module.refresh);
				if(everyEventNotEnrolled) {
					resourcesListPass = resourcesListPass.filter((resource) => resource.completion_status == 'Not Enrolled');
					showNoEventsAlert = true;
				}
			}

			if (lessonDetail?.force_enroll) {
				resourcesListPass = resourcesListPass.filter((resource) => resource.completion_status == 'Not Enrolled');
			}

			if (
				lessonDetail?.module?.is_course == 1 &&
				resourcesListPass.length > 0 &&
				$scope.showEventsEnrolListingModal &&
				(
					everyEventNotEnrolled ||
					lessonDetail?.force_enroll
				)
			) {
				event.preventDefault();
				resourcesListPass = resourcesListPass.map((data) => $scheduleService.prepareEventData(data));

				// that means user has accessed lesson open modal here
				modalInstance = $uibModal.open({
					animation: true,
					ariaLabelledBy: 'modal-title',
					ariaDescribedBy: 'modal-body',
					templateUrl: '<?=$LMSTplsUriHTML?>modal-enrole_any_events.html?v=<?=$version?>',
					controller: 'ModalEnrolAnyEvents',
					size: 'lg',
					backdrop: 'static',
					resolve: {
						data: function () {
							return {
								lessonName : lessonDetail.module.name,
								data : resourcesListPass
							};
						}
					}
				}).result.then(
					function (result) {
						$scope.showEventsEnrolListingModal = true;
						returnHere = false;
					},
					function (reason) {
						$scope.showEventsEnrolListingModal = true;
						returnHere = false;
					}
				);

				$scope.showEventsEnrolListingModal = false;

				return;
			} else if (
				lessonDetail &&
				lessonDetail.module &&
				lessonDetail.module.is_course == 1 &&
				showNoEventsAlert &&
				notEnrolledEventNotExists &&
				$scope.showEventsEnrolListingModal &&
				lessonDetail.open_in_events_only
			) {
				event.preventDefault();

				$ngConfirm({
					title: 'No Events Available!',
					content: 'There are currently no events available for this topic - Please check back at a later date',
					columnClass: 'col-md-6 col-md-offset-2 col-sm-8 col-sm-offset-1 col-xs-12',
					buttons: {
						ok: {
							text: 'ok',
							btnClass: 'btn-blue',
							action: function(scope, button){
								$scope.$apply(function() {
									$scope.showEventsEnrolListingModal = true;
									returnHere = false;
								});
								return true;
							}
						}
					}
				});

				$scope.showEventsEnrolListingModal = false;

				return ;
			}

			if (
				returnHere &&
				!$scope.showEventsEnrolListingModal
			) {
				return ;
			}

		};

		$scope.taskList = function() {
			$timeout(function () {
				$location.path('/learner/tasks/list');
			}, 100);
		};
	})
;
